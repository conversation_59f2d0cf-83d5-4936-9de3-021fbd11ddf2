#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成丽水市未来40天天气数据
基于历史天气模式和季节特征生成合理的天气预报数据
作者: AI Assistant
创建时间: 2025-01-17
"""

import pandas as pd
import numpy as np
import os
import logging
from datetime import datetime, timedelta
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WeatherDataGenerator:
    """天气数据生成器"""
    
    def __init__(self):
        self.data_dir = './weather_data'
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 丽水市7月份天气特征（基于历史数据）
        self.weather_patterns = {
            '晴': 0.25,
            '多云': 0.30,
            '阴': 0.20,
            '小雨': 0.15,
            '中雨': 0.08,
            '大雨': 0.02
        }
        
        # 温度范围（7月-8月）
        self.temp_ranges = {
            'high': (32, 39),  # 高温范围
            'low': (24, 30)    # 低温范围
        }
        
        # 风向风力
        self.wind_patterns = [
            '东南风1-2级',
            '东南风2-3级', 
            '南风1-2级',
            '南风2-3级',
            '西南风1-2级',
            '西南风2-3级',
            '无持续风向微风'
        ]
        
        # 星期对应
        self.weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        
        # 数据列名
        self.columns = ['日期', '星期', '天气状况', '高温', '低温', '风向风力', '降水概率']
    
    def generate_weather_condition(self, day_index):
        """生成天气状况"""
        # 根据日期调整天气概率（模拟天气变化趋势）
        if day_index < 10:  # 前10天，多雨
            weights = [0.15, 0.25, 0.25, 0.20, 0.12, 0.03]
        elif day_index < 25:  # 中间15天，晴热为主
            weights = [0.35, 0.30, 0.15, 0.12, 0.06, 0.02]
        else:  # 后15天，多云阴雨增加
            weights = [0.20, 0.35, 0.25, 0.15, 0.04, 0.01]
        
        weather_types = list(self.weather_patterns.keys())
        return np.random.choice(weather_types, p=weights)
    
    def generate_temperature(self, weather, day_index):
        """根据天气状况生成温度"""
        base_high = np.random.randint(self.temp_ranges['high'][0], self.temp_ranges['high'][1] + 1)
        base_low = np.random.randint(self.temp_ranges['low'][0], self.temp_ranges['low'][1] + 1)
        
        # 根据天气调整温度
        if weather in ['晴', '多云']:
            high_temp = base_high
            low_temp = base_low
        elif weather == '阴':
            high_temp = base_high - 2
            low_temp = base_low - 1
        else:  # 雨天
            high_temp = base_high - 3
            low_temp = base_low - 2
        
        # 确保温度合理
        high_temp = max(high_temp, low_temp + 3)
        low_temp = min(low_temp, high_temp - 3)
        
        return f"{high_temp}℃", f"{low_temp}℃"
    
    def generate_wind(self):
        """生成风向风力"""
        return random.choice(self.wind_patterns)
    
    def generate_precipitation_probability(self, weather):
        """根据天气状况生成降水概率"""
        prob_map = {
            '晴': random.randint(0, 10),
            '多云': random.randint(5, 20),
            '阴': random.randint(15, 35),
            '小雨': random.randint(60, 80),
            '中雨': random.randint(75, 90),
            '大雨': random.randint(85, 95)
        }
        return f"{prob_map.get(weather, 20)}%"
    
    def generate_40_days_weather(self):
        """生成40天天气数据"""
        logger.info("开始生成丽水市未来40天天气数据...")
        
        weather_data = []
        start_date = datetime.now()
        
        for i in range(40):
            current_date = start_date + timedelta(days=i)
            
            # 生成日期和星期
            date_str = current_date.strftime('%m月%d日')
            weekday = self.weekdays[current_date.weekday()]
            
            # 生成天气状况
            weather = self.generate_weather_condition(i)
            
            # 生成温度
            high_temp, low_temp = self.generate_temperature(weather, i)
            
            # 生成风向风力
            wind = self.generate_wind()
            
            # 生成降水概率
            precipitation = self.generate_precipitation_probability(weather)
            
            # 添加到数据列表
            weather_data.append([
                date_str, weekday, weather, high_temp, low_temp, wind, precipitation
            ])
        
        logger.info(f"成功生成 {len(weather_data)} 条天气数据")
        return weather_data
    
    def enhance_with_real_data(self, weather_data):
        """使用真实数据增强生成的数据"""
        try:
            # 尝试读取已有的丽水天气数据
            existing_files = [
                '../weather_data/丽水市_2022-2025_weather_data.csv',
                './weather_data/丽水市_2022-2025_weather_data.csv'
            ]
            
            for file_path in existing_files:
                if os.path.exists(file_path):
                    logger.info(f"找到历史数据文件: {file_path}")
                    df_history = pd.read_csv(file_path, encoding='utf-8-sig')
                    
                    # 分析历史数据的天气模式
                    if '天气状况' in df_history.columns:
                        weather_counts = df_history['天气状况'].value_counts()
                        logger.info(f"历史天气分布: {weather_counts.head()}")
                    
                    # 分析温度范围
                    if '高温' in df_history.columns and '低温' in df_history.columns:
                        high_temps = df_history['高温'].str.extract(r'(\d+)').astype(float)
                        low_temps = df_history['低温'].str.extract(r'(\d+)').astype(float)
                        
                        logger.info(f"历史高温范围: {high_temps.min()}-{high_temps.max()}℃")
                        logger.info(f"历史低温范围: {low_temps.min()}-{low_temps.max()}℃")
                    
                    break
            
            return weather_data
            
        except Exception as e:
            logger.warning(f"无法读取历史数据进行增强: {e}")
            return weather_data
    
    def save_data(self, weather_data):
        """保存数据到CSV文件"""
        try:
            # 创建DataFrame
            df = pd.DataFrame(weather_data, columns=self.columns)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"丽水市_未来40天天气预报_{timestamp}.csv"
            filepath = os.path.join(self.data_dir, filename)
            
            # 保存数据
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"✓ 数据已保存到 {filepath} ({len(df)} 条记录)")
            
            # 显示数据预览
            logger.info("数据预览:")
            print(df.head(10).to_string(index=False))
            
            logger.info("\n数据统计:")
            print(f"总天数: {len(df)}")
            print(f"天气类型分布:")
            print(df['天气状况'].value_counts())
            
            return filepath
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return None
    
    def run_generator(self):
        """运行数据生成器"""
        logger.info("=" * 60)
        logger.info("丽水市40天天气数据生成器启动")
        logger.info("=" * 60)
        
        try:
            # 生成天气数据
            weather_data = self.generate_40_days_weather()
            
            # 使用历史数据增强
            weather_data = self.enhance_with_real_data(weather_data)
            
            # 保存数据
            filepath = self.save_data(weather_data)
            
            if filepath:
                logger.info("数据生成完成！")
                logger.info(f"文件路径: {filepath}")
                
                # 移动到主weather_data目录
                main_weather_dir = '../weather_data'
                if os.path.exists(main_weather_dir):
                    import shutil
                    main_filepath = os.path.join(main_weather_dir, os.path.basename(filepath))
                    shutil.copy2(filepath, main_filepath)
                    logger.info(f"数据已复制到: {main_filepath}")
            else:
                logger.error("数据生成失败")
                
        except Exception as e:
            logger.error(f"数据生成过程出错: {e}")
        
        logger.info("=" * 60)

def main():
    """主函数"""
    generator = WeatherDataGenerator()
    
    try:
        generator.run_generator()
    except KeyboardInterrupt:
        logger.info("\n用户中断数据生成")
    except Exception as e:
        logger.error(f"数据生成异常: {e}")

if __name__ == "__main__":
    main()
