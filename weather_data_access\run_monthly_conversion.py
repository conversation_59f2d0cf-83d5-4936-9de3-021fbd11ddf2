#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行日天气数据转月度数据的主脚本
"""

import sys
import os

def main():
    """运行日数据转月度数据流程"""
    try:
        print("=" * 60)
        print("日天气数据转月度数据工具")
        print("=" * 60)
        
        # 导入转换模块
        from daily_to_monthly_converter import main as convert_main
        
        print("开始执行数据转换...")
        result = convert_main()
        
        if result:
            print("\n" + "=" * 60)
            print("✓ 数据转换完成！")
            print("转换后的文件已保存在 data 目录下")
            print("包含以下文件:")
            print("  - Excel格式: 浙江省月度天气数据_YYYYMMDD_HHMMSS.xlsx")
            print("  - CSV格式: 浙江省月度天气数据_YYYYMMDD_HHMMSS.csv")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("✗ 数据转换失败！")
            print("请检查日志文件 daily_to_monthly.log 获取详细错误信息")
            print("=" * 60)
            
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
        print("请确保 daily_to_monthly_converter.py 文件存在且正确")
    except Exception as e:
        print(f"✗ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
