#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行所有天气数据处理任务的主脚本
包括：
1. 代理购电数据与天气数据整合
2. 日数据转月度数据
3. 数据可视化（可选）
"""

import sys
import os

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_success(message):
    """打印成功信息"""
    print(f"✓ {message}")

def print_error(message):
    """打印错误信息"""
    print(f"✗ {message}")

def run_integration():
    """运行数据整合任务"""
    try:
        from integrate_power_weather import main as integrate_main
        print("正在执行代理购电数据与天气数据整合...")
        result = integrate_main()
        if result:
            print_success("代理购电数据与天气数据整合完成")
            return True
        else:
            print_error("代理购电数据与天气数据整合失败")
            return False
    except Exception as e:
        print_error(f"整合任务执行失败: {str(e)}")
        return False

def run_monthly_conversion():
    """运行日数据转月度数据任务"""
    try:
        from daily_to_monthly_converter import main as convert_main
        print("正在执行日数据转月度数据转换...")
        result = convert_main()
        if result:
            print_success("日数据转月度数据转换完成")
            return True
        else:
            print_error("日数据转月度数据转换失败")
            return False
    except Exception as e:
        print_error(f"转换任务执行失败: {str(e)}")
        return False

def run_visualization():
    """运行数据可视化任务"""
    try:
        # 检查是否安装了matplotlib
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
        except ImportError:
            print("⚠ 未安装matplotlib或seaborn，跳过可视化任务")
            print("  如需生成图表，请安装: pip install matplotlib seaborn")
            return True
        
        from visualize_monthly_data import main as visualize_main
        print("正在生成数据可视化图表...")
        visualize_main()
        print_success("数据可视化图表生成完成")
        return True
    except Exception as e:
        print_error(f"可视化任务执行失败: {str(e)}")
        return False

def main():
    """主函数"""
    print_header("天气数据处理工具集")
    print("本工具将依次执行以下任务：")
    print("1. 代理购电数据与天气数据整合")
    print("2. 日天气数据转月度数据")
    print("3. 数据可视化（可选）")
    
    # 询问用户要执行哪些任务
    print("\n请选择要执行的任务：")
    print("1. 全部执行")
    print("2. 仅数据整合")
    print("3. 仅日转月度")
    print("4. 仅可视化")
    print("5. 数据整合 + 日转月度")
    
    try:
        choice = input("\n请输入选择 (1-5，默认为1): ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n用户取消操作")
        return
    
    # 执行任务
    success_count = 0
    total_tasks = 0
    
    if choice in ["1", "2", "5"]:
        print_header("任务1: 代理购电数据与天气数据整合")
        total_tasks += 1
        if run_integration():
            success_count += 1
    
    if choice in ["1", "3", "5"]:
        print_header("任务2: 日天气数据转月度数据")
        total_tasks += 1
        if run_monthly_conversion():
            success_count += 1
    
    if choice in ["1", "4"]:
        print_header("任务3: 数据可视化")
        total_tasks += 1
        if run_visualization():
            success_count += 1
    
    # 显示总结
    print_header("任务执行总结")
    print(f"总任务数: {total_tasks}")
    print(f"成功完成: {success_count}")
    print(f"失败任务: {total_tasks - success_count}")
    
    if success_count == total_tasks:
        print_success("所有任务执行完成！")
        print("\n生成的文件：")
        print("📁 data/")
        print("  ├── 整合天气数据_代理购电_YYYYMMDD_HHMMSS.xlsx")
        print("  ├── 整合天气数据_代理购电_YYYYMMDD_HHMMSS.csv")
        print("  ├── 浙江省月度天气数据_YYYYMMDD_HHMMSS.xlsx")
        print("  └── 浙江省月度天气数据_YYYYMMDD_HHMMSS.csv")
        if choice in ["1", "4"]:
            print("📁 images/")
            print("  ├── 月度温度趋势.png")
            print("  ├── 月度AQI和降水趋势.png")
            print("  ├── 月度天气指数和风力趋势.png")
            print("  └── 季节性分析.png")
    else:
        print_error("部分任务执行失败，请检查日志文件获取详细信息")
        print("日志文件：")
        print("  - power_weather_integration.log")
        print("  - daily_to_monthly.log")

if __name__ == "__main__":
    main()
