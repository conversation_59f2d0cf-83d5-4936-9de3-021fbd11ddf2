#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行代理购电数据与天气数据整合的主脚本
"""

import sys
import os

def main():
    """运行数据整合流程"""
    try:
        print("=" * 60)
        print("代理购电数据与天气数据整合工具")
        print("=" * 60)
        
        # 导入整合模块
        from integrate_power_weather import main as integrate_main
        
        print("开始执行数据整合...")
        result = integrate_main()
        
        if result:
            print("\n" + "=" * 60)
            print("✓ 数据整合完成！")
            print("整合后的文件已保存在 data 目录下")
            print("包含以下文件:")
            print("  - Excel格式: 整合天气数据_代理购电_YYYYMMDD_HHMMSS.xlsx")
            print("  - CSV格式: 整合天气数据_代理购电_YYYYMMDD_HHMMSS.csv")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("✗ 数据整合失败！")
            print("请检查日志文件 power_weather_integration.log 获取详细错误信息")
            print("=" * 60)
            
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
        print("请确保 integrate_power_weather.py 文件存在且正确")
    except Exception as e:
        print(f"✗ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
