'''
模型预测器 - 用于加载训练好的模型并进行预测
支持加载指定的模型文件和配置文件，进行未来时间序列预测
'''
import sys
import json
import numpy as np
import logging
import os
from datetime import datetime

# Try to import Keras/TensorFlow with fallback

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import backend as K
from tensorflow.keras.models import load_model

from sklearn.preprocessing import MinMaxScaler
from utils import file_processing, normalize_data, inverse_normalize_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('prediction.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def TBrain_loss(y_true, y_pred, target_col_index=0):
    """
    自定义损失函数，用于加载模型时的编译
    """
    err_1 = K.mean(K.square(y_true[:, :, target_col_index] - y_pred[:, :, target_col_index]), axis=-1)
    return err_1


class ModelPredictor:
    """模型预测器类"""
    
    def __init__(self, model_path, config_path):
        """
        初始化预测器
        
        参数:
            model_path: 模型文件路径 (.h5)
            config_path: 配置文件路径 (.json)
        """
        self.model_path = model_path
        self.config_path = config_path
        self.model = None
        self.config = None
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        
        # 加载配置和模型
        self._load_config()
        self._load_model()
        
    def _load_config(self):
        """加载配置文件"""
        try:
            logger.info(f"加载配置文件: {self.config_path}")
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            logger.info("配置文件加载成功")
            logger.info(f"配置参数: {self.config}")
        except Exception as e:
            logger.error(f"配置文件加载失败: {str(e)}")
            raise
            
    def _load_model(self):
        """加载训练好的模型"""
        try:
            logger.info(f"加载模型文件: {self.model_path}")

            # 定义自定义对象以正确加载模型
            custom_objects = {
                'TBrain_loss': lambda y_true, y_pred: TBrain_loss(y_true, y_pred, self.config['target_col_index']),
                'softmax': self._softmax_activation
            }

            self.model = load_model(self.model_path, custom_objects=custom_objects, compile=False)
            logger.info("模型加载成功")
            logger.info(f"模型输入形状: {self.model.input_shape}")
            logger.info(f"模型输出形状: {self.model.output_shape}")

        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise

    def _softmax_activation(self, x, axis=1):
        """Softmax激活函数"""
        ndim = K.ndim(x)
        if ndim == 2:
            return K.softmax(x)
        elif ndim > 2:
            e = K.exp(x - K.max(x, axis=axis, keepdims=True))
            s = K.sum(e, axis=axis, keepdims=True)
            return e / s
        else:
            raise ValueError('Cannot apply softmax to a tensor that is 1D')
    
    def prepare_data(self, data_path=None):
        """
        准备预测数据
        
        参数:
            data_path: 数据文件路径，如果为None则使用配置中的路径
        
        返回:
            准备好的预测数据
        """
        try:
            if data_path is None:
                data_path = self.config['data_path']
                
            logger.info(f"准备预测数据: {data_path}")
            
            # 加载数据
            data = file_processing(data_path)
            logger.info(f"数据形状: {data.shape}")
            
            # 数据标准化
            data_normalized = normalize_data(data, self.scaler, data.shape[1])
            
            # 准备输入数据（取最后time_step个时间步）
            time_step = self.config['time_step']
            x_input = data_normalized[-time_step:]
            x_input = np.reshape(x_input, (1, x_input.shape[0], x_input.shape[1]))
            
            logger.info(f"输入数据形状: {x_input.shape}")
            return x_input, data_normalized
            
        except Exception as e:
            logger.error(f"数据准备失败: {str(e)}")
            raise
    
    def predict(self, x_input):
        """
        进行预测
        
        参数:
            x_input: 输入数据，形状为 (1, time_step, features)
        
        返回:
            预测结果
        """
        try:
            logger.info("开始预测...")
            prediction = self.model.predict(x_input, verbose=1)
            logger.info(f"预测完成，输出形状: {prediction.shape}")
            
            # 反标准化预测结果
            prediction_denormalized = inverse_normalize_data(prediction, self.scaler)
            
            return prediction_denormalized
            
        except Exception as e:
            logger.error(f"预测失败: {str(e)}")
            raise
    
    def predict_future(self, data_path=None, save_results=True):
        """
        预测未来时间序列
        
        参数:
            data_path: 数据文件路径
            save_results: 是否保存预测结果
        
        返回:
            预测结果数组
        """
        try:
            # 准备数据
            x_input, _ = self.prepare_data(data_path)
            
            # 进行预测
            prediction = self.predict(x_input)
            
            # 提取目标列的预测结果
            target_col_index = self.config['target_col_index']
            after_day = self.config['after_day']
            
            future_prediction = prediction[0, :after_day, target_col_index]
            
            logger.info(f"未来{after_day}天的预测结果:")
            for i, value in enumerate(future_prediction):
                logger.info(f"第{i+1}天: {value:.6f}")
            
            # 保存结果
            if save_results:
                self._save_prediction_results(future_prediction)
            
            return future_prediction
            
        except Exception as e:
            logger.error(f"预测过程失败: {str(e)}")
            raise
    
    def _save_prediction_results(self, prediction):
        """保存预测结果到文件"""
        try:
            # 创建输出目录
            output_dir = 'outputs'
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"prediction_results_{timestamp}.csv"
            filepath = os.path.join(output_dir, filename)
            
            # 保存为CSV文件
            import csv
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['Day', 'Predicted_Value'])
                for i, value in enumerate(prediction):
                    writer.writerow([i+1, value])
            
            logger.info(f"预测结果已保存到: {filepath}")
            
        except Exception as e:
            logger.error(f"保存预测结果失败: {str(e)}")


def main():
    """主函数"""
    # 默认模型和配置文件路径
    model_path = 'model/seq2seq_attention_2_20250714_105949.h5'
    config_path = 'configs/seq2seq_attention_2_20250714_105949_config.json'
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        logger.error(f"模型文件不存在: {model_path}")
        return
    
    if not os.path.exists(config_path):
        logger.error(f"配置文件不存在: {config_path}")
        return
    
    try:
        # 创建预测器
        predictor = ModelPredictor(model_path, config_path)
        
        # 进行预测
        results = predictor.predict_future()
        
        logger.info("预测完成！")
        
    except Exception as e:
        logger.error(f"预测过程出错: {str(e)}")


if __name__ == '__main__':
    main()
