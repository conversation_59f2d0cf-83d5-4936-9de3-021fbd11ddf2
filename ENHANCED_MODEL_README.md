# Enhanced Seq2Seq Attention Model for Time Series Prediction

## 概述 (Overview)

本项目是对原始 `seq2seq_attention_2.py` 模型的全面增强版本，专门针对 `data/整合天气数据_代理购电.xlsx` 数据集进行了优化。增强版本包含了早停机制、超参数调优、高级正则化技术、改进的模型架构等多项功能。

## 主要改进 (Key Improvements)

### 1. 数据集成和UTF-8编码支持 (Data Integration & UTF-8 Support)
- ✅ 完全支持中文字符和UTF-8编码
- ✅ 改进的Excel文件读取逻辑
- ✅ 智能数据类型检测和转换
- ✅ 数据完整性验证和异常值检测
- ✅ 详细的数据质量报告

### 2. 早停机制 (Early Stopping)
- ✅ 可配置的早停策略
- ✅ 监控指标：验证损失 (val_loss)
- ✅ 可调节的耐心值 (patience) 和最小变化阈值 (min_delta)
- ✅ 自动恢复最佳权重

### 3. 超参数配置系统 (Hyperparameter Configuration)
- ✅ 统一的配置管理类 `ModelConfig`
- ✅ JSON格式配置文件保存/加载
- ✅ 包含所有关键超参数：
  - 学习率、批次大小、隐藏层维度
  - Dropout率、正则化参数
  - 训练轮数、早停参数

### 4. 模型架构增强 (Enhanced Model Architecture)
- ✅ 改进的注意力机制
- ✅ 层归一化 (Layer Normalization)
- ✅ 批归一化 (Batch Normalization) 选项
- ✅ L1/L2正则化
- ✅ 增强的Dropout策略
- ✅ 可配置的编码器/解码器单元数

### 5. 高级训练功能 (Advanced Training Features)
- ✅ 学习率调度 (ReduceLROnPlateau)
- ✅ 模型检查点保存
- ✅ CSV训练日志记录
- ✅ 详细的训练过程监控

### 6. 错误处理和日志 (Error Handling & Logging)
- ✅ 全面的异常处理
- ✅ 详细的日志记录系统
- ✅ 训练过程可视化
- ✅ 数据验证和质量检查

### 7. 模型预测和评价系统 (Model Prediction & Evaluation System) - 新增
- ✅ 独立的模型预测器 (`model_predictor.py`)
- ✅ 自动保存评价指标到CSV文件
- ✅ 支持加载指定模型和配置文件
- ✅ 预测结果自动保存和时间戳标记
- ✅ 详细的预测日志记录

## 文件结构 (File Structure)

```
├── seq2seq_attention_2.py          # 增强版主模型文件
├── model_predictor.py               # 模型预测器（新增）
├── utils.py                        # 改进的工具函数
├── test_enhanced_model.py           # 综合测试套件
├── requirements.txt                 # 更新的依赖列表
├── ENHANCED_MODEL_README.md         # 本文档
├── data/
│   └── 整合天气数据_代理购电.xlsx    # 数据集
├── configs/                         # 配置文件目录
├── logs/                           # 训练日志目录
├── model/                          # 模型保存目录
├── outputs/                        # 输出文件目录（新增）
│   ├── evaluation_metrics/         # 评价指标文件
│   └── prediction_results/         # 预测结果文件
└── images/                         # 结果图像目录
    ├── result/                     # 预测结果图
    └── loss/                       # 损失曲线图
```

## 使用方法 (Usage)

### 1. 环境设置
```bash
pip install -r requirements.txt
```

### 2. 运行增强版模型
```bash
python seq2seq_attention_2.py
```

### 3. 使用模型预测器（新功能）
```bash
python model_predictor.py
```

### 4. 自定义预测
```python
from model_predictor import ModelPredictor

# 创建预测器实例
predictor = ModelPredictor(
    model_path='model/seq2seq_attention_2_20250714_092913.h5',
    config_path='configs/seq2seq_attention_2_20250714_092913_config.json'
)

# 进行预测
results = predictor.predict_future()
print(f"预测结果: {results}")
```



## 配置参数 (Configuration Parameters)

### 数据参数 (Data Parameters)
- `validate_percent`: 训练集比例 (默认: 0.8)
- `time_step`: 输入序列长度 (默认: 30)
- `after_day`: 预测天数 (默认: 30)
- `target_col_index`: 目标列索引 (默认: 0)

### 模型架构参数 (Model Architecture)
- `encoder_units`: 编码器LSTM单元数 (默认: 100)
- `decoder_units`: 解码器LSTM单元数 (默认: 100)
- `attention_units`: 注意力层单元数 (默认: 50)
- `dropout_rate`: Dropout率 (默认: 0.2)
- `use_layer_norm`: 是否使用层归一化 (默认: True)

### 正则化参数 (Regularization)
- `l1_reg`: L1正则化系数 (默认: 0.0001)
- `l2_reg`: L2正则化系数 (默认: 0.0001)

### 训练参数 (Training Parameters)
- `batch_size`: 批次大小 (默认: 64)
- `epochs`: 训练轮数 (默认: 200)
- `learning_rate`: 学习率 (默认: 0.001)
- `patience`: 早停耐心值 (默认: 20)

## 数据集信息 (Dataset Information)

**文件**: `data/整合天气数据_代理购电.xlsx`
- **样本数**: 1,163
- **特征数**: 6
- **列名**: 日期, 代理购电, 高温_数值, 低温_数值, AQI, 降水量
- **时间范围**: 2022-05-01 至 2025-06-30
- **目标变量**: 代理购电 (电力需求预测)

## 性能改进 (Performance Improvements)

### 训练效率
- 早停机制减少不必要的训练时间
- 学习率调度提高收敛速度
- 批处理优化提升GPU利用率

### 模型精度
- 改进的注意力机制提高预测准确性
- 正则化技术防止过拟合
- 层归一化稳定训练过程

### 代码质量
- 全面的错误处理和日志记录
- 模块化设计便于维护和扩展
- 综合测试套件确保代码质量

## 测试覆盖 (Test Coverage)

测试套件包含以下测试类：
- `TestDataProcessing`: 数据处理功能测试
- `TestModelConfig`: 配置系统测试
- `TestModelComponents`: 模型组件测试
- `TestIntegration`: 端到端集成测试

## 输出文件 (Output Files)

### 模型文件
- `model/{model_name}_{timestamp}.h5`: 训练好的模型
- `model/{model_name}_{timestamp}_best.h5`: 最佳检查点

### 日志文件
- `logs/{model_name}_{timestamp}_training.csv`: 训练日志
- `training.log`: 详细训练日志
- `prediction.log`: 预测过程日志（新增）

### 结果文件
- `outputs/output_{model_name}_{timestamp}.csv`: 预测结果
- `outputs/{model_name}_evaluation_metrics.csv`: 评价指标文件（新增）
- `outputs/prediction_results_{timestamp}.csv`: 独立预测结果（新增）
- `images/result/result_{model_name}_{timestamp}_*.png`: 预测对比图
- `images/loss/loss_{model_name}_{timestamp}.png`: 损失曲线图

## 新增功能详细说明 (New Features Details)

### 1. 模型预测器 (Model Predictor)
`model_predictor.py` 提供了独立的模型预测功能：

**主要特性**:
- 自动加载指定的模型文件和配置文件
- 支持自定义数据路径进行预测
- 自动数据预处理和标准化
- 预测结果自动反标准化
- 结果保存为CSV格式，包含时间戳

**使用示例**:
```python
# 使用默认模型和配置
python model_predictor.py

# 或在代码中使用
predictor = ModelPredictor(
    'model/seq2seq_attention_2_20250714_092913.h5',
    'configs/seq2seq_attention_2_20250714_092913_config.json'
)
results = predictor.predict_future()
```

### 2. 评价指标自动保存 (Automatic Metrics Saving)
训练过程中自动保存详细的评价指标：

**保存的指标包括**:
- 训练集和验证集的损失、MAE、MSE、RMSE
- 训练历史中的最佳和最终指标
- 训练轮数和时间戳
- 模型名称和配置信息

**文件格式**: `outputs/{model_name}_evaluation_metrics.csv`
```csv
Metric,Value
Model_Name,seq2seq_attention_2_20250714_092913
Train_Loss,0.00123456
Train_RMSE,0.03512345
Validation_Loss,0.00234567
...
```

## 故障排除 (Troubleshooting)

### 常见问题
1. **内存不足**: 减少 `batch_size` 或 `time_step`
2. **训练过慢**: 增加 `batch_size` 或减少 `epochs`
3. **过拟合**: 增加 `dropout_rate` 或正则化系数
4. **欠拟合**: 增加模型复杂度或减少正则化
5. **模型加载失败**: 确保模型文件和配置文件路径正确
6. **预测结果异常**: 检查输入数据格式和标准化过程

### 日志分析
- 查看 `training.log` 文件获取详细的训练信息和错误诊断
- 查看 `prediction.log` 文件获取预测过程的详细信息
- 检查 `outputs/` 目录下的评价指标文件分析模型性能

## 贡献指南 (Contributing)

1. 运行测试套件确保所有测试通过
2. 添加新功能时请包含相应的测试
3. 遵循现有的代码风格和注释规范
4. 更新文档说明新增功能

## 许可证 (License)

本项目继承原项目的许可证条款。

---

**注意**: 本增强版本完全兼容原始数据集，并提供了显著的性能和功能改进。建议在生产环境中使用前进行充分的测试和验证。
