2025-07-16 09:32:37,059 - INFO - 开始执行代理购电数据与天气数据整合任务...
2025-07-16 09:32:37,059 - INFO - ============================================================
2025-07-16 09:32:37,059 - INFO - 步骤1: 加载代理购电数据
2025-07-16 09:32:37,682 - ERROR - 加载代理购电数据失败: [Errno 2] No such file or directory: '../data/李娟娟代理购电.xlsx'
2025-07-16 09:32:37,682 - ERROR - 加载代理购电数据失败，程序终止
2025-07-16 09:32:51,039 - INFO - 开始执行代理购电数据与天气数据整合任务...
2025-07-16 09:32:51,039 - INFO - ============================================================
2025-07-16 09:32:51,039 - INFO - 步骤1: 加载代理购电数据
2025-07-16 09:32:51,637 - INFO - 成功加载代理购电数据: ./data/李娟娟代理购电.xlsx
2025-07-16 09:32:51,637 - INFO - 数据形状: (1163, 4)
2025-07-16 09:32:51,637 - INFO - 列名: ['日期', '居民', '农业', '代理购电']
2025-07-16 09:32:51,637 - INFO - 检测到YYYYMMDD格式的日期，正在转换...
2025-07-16 09:32:51,637 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-06 00:00:00
2025-07-16 09:32:51,637 - INFO - 步骤2: 加载天气数据
2025-07-16 09:32:51,661 - INFO - 成功加载天气数据: ./weather_data/浙江省聚合天气数据.csv
2025-07-16 09:32:51,661 - INFO - 数据形状: (1171, 9)
2025-07-16 09:32:51,661 - INFO - 列名: ['日期', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:32:51,664 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-14 00:00:00
2025-07-16 09:32:51,665 - INFO - 步骤3: 整合数据
2025-07-16 09:32:51,668 - INFO - 代理购电数据有效记录: 1163 条
2025-07-16 09:32:51,668 - INFO - 天气数据有效记录: 1171 条
2025-07-16 09:32:51,676 - INFO - 数据整合完成，使用 inner 策略
2025-07-16 09:32:51,676 - INFO - 整合后记录数: 1163 条
2025-07-16 09:32:51,676 - INFO - 整合后列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:32:51,677 - INFO - 整合后日期范围: 2022-05-01 00:00:00 到 2025-07-06 00:00:00
2025-07-16 09:32:51,678 - INFO - 步骤4: 保存整合后的数据
2025-07-16 09:32:51,938 - INFO - 整合数据已保存为Excel文件: ../data\整合天气数据_代理购电_20250716_093251.xlsx
2025-07-16 09:32:51,961 - INFO - 整合数据已保存为CSV文件: ../data\整合天气数据_代理购电_20250716_093251.csv
2025-07-16 09:32:51,961 - INFO - 数据整合任务完成！
2025-07-16 09:32:51,961 - INFO - 数据摘要:
2025-07-16 09:32:51,961 - INFO -   总记录数: 1163
2025-07-16 09:32:51,961 - INFO -   日期范围: 2022-05-01 00:00:00 到 2025-07-06 00:00:00
2025-07-16 09:32:51,961 - INFO -   列数: 12
2025-07-16 09:32:51,961 - INFO -   列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:32:51,961 - INFO - ============================================================
2025-07-16 09:33:23,524 - INFO - 开始执行代理购电数据与天气数据整合任务...
2025-07-16 09:33:23,524 - INFO - ============================================================
2025-07-16 09:33:23,525 - INFO - 步骤1: 加载代理购电数据
2025-07-16 09:33:24,091 - INFO - 成功加载代理购电数据: ./data/李娟娟代理购电.xlsx
2025-07-16 09:33:24,091 - INFO - 数据形状: (1163, 4)
2025-07-16 09:33:24,091 - INFO - 列名: ['日期', '居民', '农业', '代理购电']
2025-07-16 09:33:24,091 - INFO - 检测到YYYYMMDD格式的日期，正在转换...
2025-07-16 09:33:24,091 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-06 00:00:00
2025-07-16 09:33:24,091 - INFO - 步骤2: 加载天气数据
2025-07-16 09:33:24,108 - INFO - 成功加载天气数据: ./weather_data/浙江省聚合天气数据.csv
2025-07-16 09:33:24,109 - INFO - 数据形状: (1171, 9)
2025-07-16 09:33:24,109 - INFO - 列名: ['日期', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:33:24,111 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-14 00:00:00
2025-07-16 09:33:24,111 - INFO - 步骤3: 整合数据
2025-07-16 09:33:24,111 - INFO - 代理购电数据有效记录: 1163 条
2025-07-16 09:33:24,111 - INFO - 天气数据有效记录: 1171 条
2025-07-16 09:33:24,118 - INFO - 数据整合完成，使用 inner 策略
2025-07-16 09:33:24,118 - INFO - 整合后记录数: 1163 条
2025-07-16 09:33:24,119 - INFO - 整合后列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:33:24,119 - INFO - 整合后日期范围: 2022-05-01 00:00:00 到 2025-07-06 00:00:00
2025-07-16 09:33:24,119 - INFO - 步骤4: 保存整合后的数据
2025-07-16 09:33:24,383 - INFO - 整合数据已保存为Excel文件: ../data\整合天气数据_代理购电_20250716_093324.xlsx
2025-07-16 09:33:24,399 - INFO - 整合数据已保存为CSV文件: ../data\整合天气数据_代理购电_20250716_093324.csv
2025-07-16 09:33:24,399 - INFO - 数据整合任务完成！
2025-07-16 09:33:24,400 - INFO - 数据摘要:
2025-07-16 09:33:24,400 - INFO -   总记录数: 1163
2025-07-16 09:33:24,401 - INFO -   日期范围: 2022-05-01 00:00:00 到 2025-07-06 00:00:00
2025-07-16 09:33:24,401 - INFO -   列数: 12
2025-07-16 09:33:24,401 - INFO -   列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:33:24,402 - INFO - ============================================================
2025-07-16 09:36:07,770 - INFO - 开始执行代理购电数据与天气数据整合任务...
2025-07-16 09:36:07,771 - INFO - ============================================================
2025-07-16 09:36:07,771 - INFO - 步骤1: 加载代理购电数据
2025-07-16 09:36:08,343 - INFO - 成功加载代理购电数据: ./data/李娟娟代理购电.xlsx
2025-07-16 09:36:08,343 - INFO - 数据形状: (1170, 4)
2025-07-16 09:36:08,343 - INFO - 列名: ['日期', '居民', '农业', '代理购电']
2025-07-16 09:36:08,343 - INFO - 检测到YYYYMMDD格式的日期，正在转换...
2025-07-16 09:36:08,343 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-13 00:00:00
2025-07-16 09:36:08,343 - INFO - 步骤2: 加载天气数据
2025-07-16 09:36:08,359 - INFO - 成功加载天气数据: ./weather_data/浙江省聚合天气数据.csv
2025-07-16 09:36:08,360 - INFO - 数据形状: (1171, 9)
2025-07-16 09:36:08,360 - INFO - 列名: ['日期', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:36:08,362 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-14 00:00:00
2025-07-16 09:36:08,363 - INFO - 步骤3: 整合数据
2025-07-16 09:36:08,365 - INFO - 代理购电数据有效记录: 1170 条
2025-07-16 09:36:08,365 - INFO - 天气数据有效记录: 1171 条
2025-07-16 09:36:08,368 - INFO - 数据整合完成，使用 inner 策略
2025-07-16 09:36:08,368 - INFO - 整合后记录数: 1170 条
2025-07-16 09:36:08,369 - INFO - 整合后列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:36:08,369 - INFO - 整合后日期范围: 2022-05-01 00:00:00 到 2025-07-13 00:00:00
2025-07-16 09:36:08,369 - INFO - 步骤4: 保存整合后的数据
2025-07-16 09:36:08,641 - INFO - 整合数据已保存为Excel文件: ../data\整合天气数据_代理购电_20250716_093608.xlsx
2025-07-16 09:36:08,665 - INFO - 整合数据已保存为CSV文件: ../data\整合天气数据_代理购电_20250716_093608.csv
2025-07-16 09:36:08,665 - INFO - 数据整合任务完成！
2025-07-16 09:36:08,665 - INFO - 数据摘要:
2025-07-16 09:36:08,665 - INFO -   总记录数: 1170
2025-07-16 09:36:08,665 - INFO -   日期范围: 2022-05-01 00:00:00 到 2025-07-13 00:00:00
2025-07-16 09:36:08,665 - INFO -   列数: 12
2025-07-16 09:36:08,665 - INFO -   列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:36:08,665 - INFO - ============================================================
2025-07-16 09:39:03,120 - INFO - 开始执行代理购电数据与天气数据整合任务...
2025-07-16 09:39:03,120 - INFO - ============================================================
2025-07-16 09:39:03,120 - INFO - 步骤1: 加载代理购电数据
2025-07-16 09:39:03,695 - INFO - 成功加载代理购电数据: ./data/李娟娟代理购电.xlsx
2025-07-16 09:39:03,695 - INFO - 数据形状: (1170, 4)
2025-07-16 09:39:03,695 - INFO - 列名: ['日期', '居民', '农业', '代理购电']
2025-07-16 09:39:03,695 - INFO - 检测到YYYYMMDD格式的日期，正在转换...
2025-07-16 09:39:03,695 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-13 00:00:00
2025-07-16 09:39:03,695 - INFO - 步骤2: 加载天气数据
2025-07-16 09:39:03,695 - INFO - 成功加载天气数据: ./weather_data/浙江省聚合天气数据.csv
2025-07-16 09:39:03,695 - INFO - 数据形状: (1171, 9)
2025-07-16 09:39:03,695 - INFO - 列名: ['日期', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:39:03,709 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-14 00:00:00
2025-07-16 09:39:03,710 - INFO - 步骤3: 整合数据
2025-07-16 09:39:03,712 - INFO - 代理购电数据有效记录: 1170 条
2025-07-16 09:39:03,712 - INFO - 天气数据有效记录: 1171 条
2025-07-16 09:39:03,716 - INFO - 数据整合完成，使用 inner 策略
2025-07-16 09:39:03,716 - INFO - 整合后记录数: 1170 条
2025-07-16 09:39:03,716 - INFO - 整合后列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:39:03,717 - INFO - 整合后日期范围: 2022-05-01 00:00:00 到 2025-07-13 00:00:00
2025-07-16 09:39:03,717 - INFO - 步骤4: 保存整合后的数据
2025-07-16 09:39:03,970 - INFO - 整合数据已保存为Excel文件: ./data\整合天气数据_代理购电_20250716_093903.xlsx
2025-07-16 09:39:03,989 - INFO - 整合数据已保存为CSV文件: ./data\整合天气数据_代理购电_20250716_093903.csv
2025-07-16 09:39:03,990 - INFO - 数据整合任务完成！
2025-07-16 09:39:03,992 - INFO - 数据摘要:
2025-07-16 09:39:03,992 - INFO -   总记录数: 1170
2025-07-16 09:39:03,993 - INFO -   日期范围: 2022-05-01 00:00:00 到 2025-07-13 00:00:00
2025-07-16 09:39:03,993 - INFO -   列数: 12
2025-07-16 09:39:03,993 - INFO -   列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:39:03,993 - INFO - ============================================================
2025-07-16 09:43:26,840 - INFO - 开始执行代理购电数据与天气数据整合任务...
2025-07-16 09:43:26,840 - INFO - ============================================================
2025-07-16 09:43:26,840 - INFO - 步骤1: 加载代理购电数据
2025-07-16 09:43:27,422 - INFO - 成功加载代理购电数据: ./data/龙泉代理购电.xlsx
2025-07-16 09:43:27,423 - INFO - 数据形状: (1170, 4)
2025-07-16 09:43:27,423 - INFO - 列名: ['日期', '居民', '农业', '代理购电']
2025-07-16 09:43:27,423 - INFO - 检测到YYYYMMDD格式的日期，正在转换...
2025-07-16 09:43:27,429 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-13 00:00:00
2025-07-16 09:43:27,429 - INFO - 步骤2: 加载天气数据
2025-07-16 09:43:27,434 - INFO - 成功加载天气数据: ./weather_data/浙江省聚合天气数据.csv
2025-07-16 09:43:27,434 - INFO - 数据形状: (1171, 9)
2025-07-16 09:43:27,434 - INFO - 列名: ['日期', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:43:27,437 - INFO - 日期范围: 2022-05-01 00:00:00 到 2025-07-14 00:00:00
2025-07-16 09:43:27,437 - INFO - 步骤3: 整合数据
2025-07-16 09:43:27,440 - INFO - 代理购电数据有效记录: 1170 条
2025-07-16 09:43:27,440 - INFO - 天气数据有效记录: 1171 条
2025-07-16 09:43:27,448 - INFO - 数据整合完成，使用 inner 策略
2025-07-16 09:43:27,448 - INFO - 整合后记录数: 1170 条
2025-07-16 09:43:27,449 - INFO - 整合后列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:43:27,450 - INFO - 整合后日期范围: 2022-05-01 00:00:00 到 2025-07-13 00:00:00
2025-07-16 09:43:27,450 - INFO - 步骤4: 保存整合后的数据
2025-07-16 09:43:27,746 - INFO - 整合数据已保存为Excel文件: ./data\整合天气数据_代理购电_20250716_094327.xlsx
2025-07-16 09:43:27,766 - INFO - 整合数据已保存为CSV文件: ./data\整合天气数据_代理购电_20250716_094327.csv
2025-07-16 09:43:27,767 - INFO - 数据整合任务完成！
2025-07-16 09:43:27,768 - INFO - 数据摘要:
2025-07-16 09:43:27,768 - INFO -   总记录数: 1170
2025-07-16 09:43:27,768 - INFO -   日期范围: 2022-05-01 00:00:00 到 2025-07-13 00:00:00
2025-07-16 09:43:27,768 - INFO -   列数: 12
2025-07-16 09:43:27,768 - INFO -   列名: ['日期', '居民', '农业', '代理购电', '高温_数值', '低温_数值', 'AQI', '降水量', '天气状况', '风向风力', '高温', '低温']
2025-07-16 09:43:27,768 - INFO - ============================================================
