import os
import csv
import pandas as pd
import errno
import numpy as np
import matplotlib.pyplot as plt

# Try to import Keras/TensorFlow with fallback
try:
    from keras.utils.vis_utils import plot_model
except ImportError:
    try:
        from tensorflow.keras.utils import plot_model
    except ImportError:
        def plot_model(*args, **kwargs):
            print("Warning: plot_model not available. Skipping model visualization.")


def file_processing(file_path, encode='utf-8'):
    """
    从Excel文件读取数据并进行预处理，支持UTF-8编码和中文字符

    参数:
        file_path: Excel文件路径
        encode: 编码格式，默认为utf-8

    返回:
        numpy数组，形状为 (samples, features)
        其中samples为数据行数，features为特征列数（跳过日期列）
    """
    import pandas as pd
    import numpy as np
    import logging

    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    try:
        # 读取Excel文件，确保正确处理中文字符
        logger.info(f"正在读取数据文件: {file_path}")
        df = pd.read_excel(file_path, engine='openpyxl')

        # 验证数据完整性
        if df.empty:
            raise ValueError("数据文件为空")

        logger.info(f"数据形状: {df.shape}")
        logger.info(f"列名: {df.columns.tolist()}")

        # 检查缺失值
        missing_values = df.isnull().sum()
        if missing_values.any():
            logger.warning(f"发现缺失值: {missing_values[missing_values > 0].to_dict()}")

        # 跳过日期列，提取数值特征
        # 假设第一列是日期，其余列是特征
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        if len(numeric_columns) == 0:
            raise ValueError("未找到数值列")

        # 提取数值数据
        data = df[numeric_columns].values

        logger.info(f"提取的特征数量: {data.shape[1]}")
        logger.info(f"数据样本数量: {data.shape[0]}")

        # 验证数据类型
        if not np.issubdtype(data.dtype, np.number):
            logger.warning("数据包含非数值类型，尝试转换...")
            data = data.astype(float)

        # 检查无穷大和NaN值
        if np.any(np.isinf(data)) or np.any(np.isnan(data)):
            logger.warning("数据包含无穷大或NaN值，进行清理...")
            data = np.nan_to_num(data, nan=0.0, posinf=0.0, neginf=0.0)

        logger.info("数据处理完成")
        return data

    except Exception as e:
        logger.error(f"数据处理失败: {str(e)}")
        raise


def normalize_data(data, scaler, feature_len):
    """
    使用MinMaxScaler对数据进行标准化处理
    
    参数:
        data: 原始数据，形状为 (samples, features)
        scaler: MinMaxScaler对象
        feature_len: 特征维度（此参数在当前实现中未使用）
    
    返回:
        标准化后的数据，值域为[0, 1]
    """
    minmaxscaler = scaler.fit(data)
    normalize_data = minmaxscaler.transform(data)

    return normalize_data


def inverse_normalize_data(data, scaler):
    """
    将标准化后的数据反转换回原始数据规模
    
    参数:
        data: 标准化后的数据，形状为 (samples, time_steps, features)
        scaler: 用于反转换的MinMaxScaler对象
    
    返回:
        反标准化后的数据，恢复原始数据规模
    """
    for i in range(len(data)):
        data[i] = scaler.inverse_transform(data[i])

    return data


def generate_output(output, model_name):
    """
    只保存预测的数据到CSV文件，不做任何额外处理
    参数:
        output: 预测结果数组，形状为 (samples, days)
        model_name: 模型名称，用于生成输出文件名
    """
    file_path = 'outputs/output_{}.csv'.format(model_name)
    if not os.path.exists(os.path.dirname(file_path)):
        try:
            os.makedirs(os.path.dirname(file_path))
        except OSError as exc:  # Guard against race condition
            if exc.errno != errno.EEXIST:
                raise

    with open(file_path, 'w+', newline='') as file:
        w = csv.writer(file)
        w.writerows(output)


def plot_model_architecture(model, model_name):
    """
    绘制模型架构图并保存为PNG文件
    
    参数:
        model: Keras模型对象
        model_name: 模型名称，用于生成文件名
    """
    file_path = './images/model/{}.png'.format(model_name[-12:])
    if not os.path.exists(os.path.dirname(file_path)):
        try:
            os.makedirs(os.path.dirname(file_path))
        except OSError as exc:  # Guard against race condition
            if exc.errno != errno.EEXIST:
                raise

    plot_model(model, to_file=file_path, show_shapes=True)


def save_model(model, model_name):
    """
    保存训练好的模型为HDF5格式文件
    
    参数:
        model: 训练好的Keras模型对象
        model_name: 模型名称，用于生成文件名
    """
    file_path = 'model/{}.h5'.format(model_name)
    if not os.path.exists(os.path.dirname(file_path)):
        try:
            os.makedirs(os.path.dirname(file_path))
        except OSError as exc:  # Guard against race condition
            if exc.errno != errno.EEXIST:
                raise

    model.save(file_path)


def plot_predict(data, data_predict, file_name,after_day,target_col_index):
    """
    每个数据块（10天）分别画一张真实值与预测值的对比图
    """
    file_path_base = 'images/result/{}'.format(file_name)
    if not os.path.exists(os.path.dirname(file_path_base)):
        try:
            os.makedirs(os.path.dirname(file_path_base))
        except OSError as exc:  # Guard against race condition
            if exc.errno != errno.EEXIST:
                raise

    num_samples = data.shape[0]

    for i in range(num_samples):
        plt.figure(figsize=(6, 4))
        # 取出当前样本的5天真实值和预测值
        real = data[i, :, target_col_index]
        pred = data_predict[i, :, target_col_index]
        plt.plot(
            range(1, after_day+1), real, marker='o', color='black',
            label='real', linewidth=1.5
        )
        plt.plot(
            range(1, after_day+1), pred, marker='o', color='red',
            label='pred', linewidth=1.5
        )
        plt.title(f'valid_compare_{i}')
        plt.xlabel('day')
        plt.ylabel('value')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'{file_path_base}_{i}.png', dpi=300, bbox_inches='tight')
        plt.close()


def plot_loss(history, file_name):
    """
    绘制训练过程中的损失曲线图
    
    显示训练损失和验证损失的变化趋势
    
    参数:
        history: 模型训练历史对象，包含loss和val_loss数据
        file_name: 文件名，用于保存图片
    """
    file_path = 'images/loss/{}.png'.format(file_name)
    if not os.path.exists(os.path.dirname(file_path)):
        try:
            os.makedirs(os.path.dirname(file_path))
        except OSError as exc:  # Guard against race condition
            if exc.errno != errno.EEXIST:
                raise

    plt.figure()

    plt.plot(history.history['loss'])
    plt.plot(history.history['val_loss'])
    plt.title('model train vs validation loss')
    plt.ylabel('loss')
    plt.xlabel('epoch')
    plt.legend(['train', 'validation'], loc='upper right')
    plt.savefig(file_path)
    # plt.show()
