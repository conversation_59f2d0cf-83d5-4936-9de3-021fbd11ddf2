#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
天气24网站爬虫 - 浙江省历史天气数据采集
目标网站: https://www.tianqi24.com/
作者: AI Assistant
创建时间: 2025-01-09
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import os
import time
import logging
from datetime import datetime, timedelta
import re
from urllib.parse import urljoin
import json

# 导入增强解析器
try:
    from tianqi24_parser_enhanced import parse_tianqi24_data
except ImportError:
    # 如果导入失败，使用内置解析方法
    parse_tianqi24_data = None

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('weather_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 设置解析器日志级别为INFO，避免过多调试信息
logging.getLogger('tianqi24_parser_enhanced').setLevel(logging.INFO)
logger = logging.getLogger(__name__)

class Tianqi24WeatherScraper:
    """天气24网站爬虫类"""
    
    def __init__(self):
        self.base_url = "https://www.tianqi24.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
        
        # 浙江省11个地级市配置
        self.cities = {
            '杭州市': 'hangzhou',
            '宁波市': 'ningbo', 
            '温州市': 'wenzhou',
            '嘉兴市': 'jiaxing',
            '湖州市': 'huzhou',
            '绍兴市': 'shaoxing',
            '金华市': 'jinhua',
            '衢州市': 'quzhou',
            '舟山市': 'zhoushan',
            '台州市': 'taizhou',
            '丽水市': 'lishui'
        }
        
        # 数据列名
        self.columns = ['日期', '天气状况', '高温', '低温', 'AQI', '风向风力', '降水量']
        
        # 创建数据保存目录
        self.data_dir = './weather_data'
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            
        # 进度记录文件
        self.progress_file = os.path.join(self.data_dir, 'scraping_progress.json')
        self.load_progress()
    
    def load_progress(self):
        """加载爬取进度"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    self.progress = json.load(f)
            else:
                self.progress = {}
        except Exception as e:
            logger.warning(f"加载进度文件失败: {e}")
            self.progress = {}
    
    def save_progress(self):
        """保存爬取进度"""
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存进度文件失败: {e}")
    
    def get_month_list(self, start_year_month, end_year_month):
        """生成年月列表"""
        start_year, start_month = map(int, start_year_month.split('-'))
        end_year, end_month = map(int, end_year_month.split('-'))
        
        months = []
        current_date = datetime(start_year, start_month, 1)
        end_date = datetime(end_year, end_month, 1)
        
        while current_date <= end_date:
            months.append(current_date.strftime('%Y%m'))
            # 移动到下个月
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
        
        return months
    
    def test_url_access(self, city_en, year_month):
        """测试URL访问"""
        url = f"{self.base_url}/{city_en}/history{year_month}.html"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            # 检查是否包含天气数据
            if '历史天气' in response.text and '日期' in response.text:
                logger.info(f"✓ URL访问成功: {url}")
                return True, response.text
            else:
                logger.warning(f"✗ URL无有效数据: {url}")
                return False, None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"✗ URL访问失败: {url} - {e}")
            return False, None
    
    def parse_weather_data(self, html_content, year_month):
        """解析天气数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找包含天气数据的表格或列表
            # 根据网页结构，数据在ul标签中，每个li包含一天的数据
            weather_list = soup.find('ul')
            if not weather_list:
                logger.warning("未找到天气数据列表")
                return []
            
            weather_data = []
            items = weather_list.find_all('li')
            
            for item in items[1:]:  # 跳过表头
                try:
                    # 提取各个字段
                    cells = item.find_all(['div', 'span', 'strong'])
                    if len(cells) < 6:
                        continue
                    
                    # 解析日期 (MM-DD格式) 并添加年份
                    date_text = item.get_text()
                    date_match = re.search(r'(\d{1,2}-\d{1,2})', date_text)
                    if not date_match:
                        continue

                    # 从year_month参数中提取年份
                    year = year_month[:4]
                    month_day = date_match.group(1)

                    # 确保月日格式为MM-DD
                    if len(month_day.split('-')[0]) == 1:
                        month, day = month_day.split('-')
                        month_day = f"{month.zfill(2)}-{day.zfill(2)}"

                    # 组合完整日期：YYYY-MM-DD
                    date = f"{year}-{month_day}"
                    
                    # 解析天气状况
                    weather_pattern = r'(雨|晴|多云|阴|雪|雾|霾)'
                    weather_matches = re.findall(weather_pattern, date_text)
                    if weather_matches:
                        if len(weather_matches) > 1:
                            weather = f"{weather_matches[0]}/{weather_matches[1]}"
                        else:
                            weather = weather_matches[0]
                    else:
                        weather = "未知"
                    
                    # 解析温度
                    temp_pattern = r'(\d+)℃'
                    temp_matches = re.findall(temp_pattern, date_text)
                    if len(temp_matches) >= 2:
                        high_temp = f"{temp_matches[0]}℃"
                        low_temp = f"{temp_matches[1]}℃"
                    else:
                        high_temp = low_temp = "未知"
                    
                    # 解析AQI
                    aqi_match = re.search(r'AQI[:\s]*(\d+)', date_text)
                    aqi = aqi_match.group(1) if aqi_match else re.search(r'\b(\d{1,3})\b', date_text)
                    aqi = aqi.group(1) if aqi else "未知"
                    
                    # 解析风向风力
                    wind_pattern = r'([东西南北]+风\d+级)'
                    wind_match = re.search(wind_pattern, date_text)
                    wind = wind_match.group(1) if wind_match else "未知"
                    
                    # 解析降水量
                    rain_pattern = r'(\d+\.?\d*)\s*(?:mm)?'
                    rain_matches = re.findall(rain_pattern, date_text)
                    rainfall = rain_matches[-1] if rain_matches else "0"
                    
                    weather_data.append([
                        date, weather, high_temp, low_temp, aqi, wind, rainfall
                    ])
                    
                except Exception as e:
                    logger.warning(f"解析单行数据失败: {e}")
                    continue
            
            logger.info(f"成功解析 {len(weather_data)} 条天气数据")
            return weather_data
            
        except Exception as e:
            logger.error(f"解析HTML失败: {e}")
            return []

    def parse_weather_data_improved(self, html_content, year_month):
        """改进的天气数据解析方法 - 基于实际网页结构"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            weather_data = []

            # 查找所有包含日期的li元素
            date_items = soup.find_all('li')

            for item in date_items:
                item_text = item.get_text(strip=True)

                # 检查是否包含日期格式 (MM-DD)
                date_match = re.search(r'(\d{1,2}-\d{1,2})', item_text)
                if not date_match:
                    continue

                # 从year_month参数中提取年份
                year = year_month[:4]
                month_day = date_match.group(1)

                # 确保日期格式为MM-DD
                if len(month_day.split('-')[0]) == 1:
                    month, day = month_day.split('-')
                    month_day = f"{month.zfill(2)}-{day.zfill(2)}"

                # 组合完整日期：YYYY-MM-DD
                date = f"{year}-{month_day}"

                try:
                    # 解析天气状况 - 查找粗体标记的天气
                    weather_elements = item.find_all(['strong', 'b'])
                    weather_parts = []

                    for elem in weather_elements:
                        text = elem.get_text(strip=True)
                        if any(w in text for w in ['雨', '晴', '多云', '阴', '雪', '雾', '霾']):
                            weather_parts.append(text)

                    # 如果没有找到粗体天气，尝试从整个文本中提取
                    if not weather_parts:
                        weather_pattern = r'(雨|晴|多云|阴|雪|雾|霾)'
                        weather_matches = re.findall(weather_pattern, item_text)
                        weather_parts = weather_matches[:2]  # 最多取两个

                    if len(weather_parts) >= 2:
                        weather = f"{weather_parts[0]}/{weather_parts[1]}"
                    elif len(weather_parts) == 1:
                        weather = weather_parts[0]
                    else:
                        weather = "未知"

                    # 解析温度
                    temp_matches = re.findall(r'(\d+)℃', item_text)
                    if len(temp_matches) >= 2:
                        high_temp = f"{temp_matches[0]}℃"
                        low_temp = f"{temp_matches[1]}℃"
                    elif len(temp_matches) == 1:
                        high_temp = f"{temp_matches[0]}℃"
                        low_temp = "未知"
                    else:
                        high_temp = low_temp = "未知"

                    # 解析AQI - 查找数字
                    aqi_matches = re.findall(r'\b(\d{1,3})\b', item_text)
                    # 过滤掉日期和温度中的数字，找到可能的AQI值
                    aqi = "未知"
                    for match in aqi_matches:
                        num = int(match)
                        if 0 <= num <= 500 and match not in temp_matches:  # AQI范围通常是0-500
                            aqi = match
                            break

                    # 解析风向风力
                    wind_pattern = r'([东西南北]+风\d+级)'
                    wind_match = re.search(wind_pattern, item_text)
                    wind = wind_match.group(1) if wind_match else "未知"

                    # 解析降水量
                    rainfall_pattern = r'(\d+\.?\d*)\s*(?:mm)?'
                    rainfall_matches = re.findall(rainfall_pattern, item_text)
                    # 过滤掉温度和AQI，找到降水量
                    rainfall = "0"
                    for match in rainfall_matches:
                        if match not in temp_matches and match != aqi:
                            try:
                                val = float(match)
                                if 0 <= val <= 1000:  # 合理的降水量范围
                                    rainfall = match
                                    break
                            except:
                                continue

                    weather_data.append([
                        date, weather, high_temp, low_temp, aqi, wind, rainfall
                    ])

                except Exception as e:
                    logger.warning(f"解析日期 {date} 数据失败: {e}")
                    continue

            logger.info(f"成功解析 {len(weather_data)} 条天气数据")
            return weather_data

        except Exception as e:
            logger.error(f"解析HTML失败: {e}")
            return []

    def scrape_city_month(self, city_cn, city_en, year_month, retry_count=3):
        """爬取单个城市单个月份的数据"""
        url = f"{self.base_url}/{city_en}/history{year_month}.html"

        for attempt in range(retry_count):
            try:
                logger.info(f"正在爬取: {city_cn} {year_month} (尝试 {attempt + 1}/{retry_count})")

                response = requests.get(url, headers=self.headers, timeout=15)
                response.raise_for_status()

                # 检查响应内容
                if '历史天气' not in response.text:
                    logger.warning(f"页面内容异常: {url}")
                    if attempt < retry_count - 1:
                        time.sleep(2)
                        continue
                    return []

                # 解析数据 - 优先使用增强解析器
                if parse_tianqi24_data:
                    weather_data = parse_tianqi24_data(response.text, year_month)
                else:
                    weather_data = self.parse_weather_data_improved(response.text, year_month)

                if weather_data:
                    logger.info(f"✓ {city_cn} {year_month}: 成功获取 {len(weather_data)} 条数据")
                    return weather_data
                else:
                    logger.warning(f"✗ {city_cn} {year_month}: 未获取到数据")
                    if attempt < retry_count - 1:
                        time.sleep(2)
                        continue
                    return []

            except requests.exceptions.RequestException as e:
                logger.error(f"网络请求失败 (尝试 {attempt + 1}): {e}")
                if attempt < retry_count - 1:
                    time.sleep(5)
                    continue
                return []
            except Exception as e:
                logger.error(f"爬取失败 (尝试 {attempt + 1}): {e}")
                if attempt < retry_count - 1:
                    time.sleep(2)
                    continue
                return []

        return []

    def save_city_data(self, city_cn, all_data):
        """保存城市数据到CSV文件"""
        if not all_data:
            logger.warning(f"{city_cn}: 没有数据需要保存")
            return False

        try:
            # 创建DataFrame
            df = pd.DataFrame(all_data, columns=self.columns)

            # 数据清洗和验证
            df = self.clean_data(df)

            # 根据数据的年份范围生成文件名
            if len(df) > 0:
                # 提取年份信息 - 现在日期格式为YYYY-MM-DD
                dates = df['日期'].astype(str)
                years = []

                for date_str in dates:
                    if len(date_str) >= 4 and date_str[:4].isdigit():
                        years.append(date_str[:4])

                years = sorted(list(set(years)))  # 去重并排序

                if len(years) == 1:
                    # 单年数据
                    filename = f"{city_cn}_{years[0]}_weather_data.csv"
                elif len(years) > 1:
                    # 多年数据
                    filename = f"{city_cn}_{years[0]}-{years[-1]}_weather_data.csv"
                else:
                    # 备用文件名
                    timestamp = datetime.now().strftime('%Y%m%d')
                    filename = f"{city_cn}_{timestamp}_weather_data.csv"
            else:
                timestamp = datetime.now().strftime('%Y%m%d')
                filename = f"{city_cn}_{timestamp}_weather_data.csv"

            filepath = os.path.join(self.data_dir, filename)
            df.to_csv(filepath, index=False, encoding='utf-8-sig')

            logger.info(f"✓ {city_cn}: 数据已保存到 {filepath} ({len(df)} 条记录)")
            return True

        except Exception as e:
            logger.error(f"✗ {city_cn}: 保存数据失败 - {e}")
            return False

    def clean_data(self, df):
        """数据清洗"""
        try:
            # 确保日期列为字符串类型
            df['日期'] = df['日期'].astype(str)

            # 去重 - 基于完整日期（包含年份）
            original_count = len(df)
            df = df.drop_duplicates(subset=['日期'])
            duplicate_count = original_count - len(df)

            if duplicate_count > 0:
                logger.info(f"去除重复数据: {duplicate_count} 条")

            # 按日期排序 - 现在日期格式为YYYY-MM-DD，可以直接字符串排序
            df = df.sort_values('日期')

            # 重置索引
            df = df.reset_index(drop=True)

            logger.info(f"数据清洗完成: {len(df)} 条记录")

            # 显示日期范围
            if len(df) > 0:
                logger.info(f"日期范围: {df['日期'].iloc[0]} 到 {df['日期'].iloc[-1]}")

            return df

        except Exception as e:
            logger.warning(f"数据清洗失败: {e}")
            return df

    def scrape_city_data(self, city_cn, city_en, months):
        """爬取单个城市的所有月份数据"""
        logger.info(f"\n开始爬取 {city_cn} 的天气数据...")

        all_data = []
        success_count = 0
        total_months = len(months)

        for i, month in enumerate(months, 1):
            # 检查进度记录
            progress_key = f"{city_cn}_{month}"
            if progress_key in self.progress:
                logger.info(f"跳过已完成: {city_cn} {month} ({i}/{total_months})")
                continue

            # 爬取数据
            month_data = self.scrape_city_month(city_cn, city_en, month)

            if month_data:
                all_data.extend(month_data)
                success_count += 1

                # 记录进度
                self.progress[progress_key] = {
                    'completed': True,
                    'timestamp': datetime.now().isoformat(),
                    'records': len(month_data)
                }
                self.save_progress()

            # 进度显示
            logger.info(f"进度: {i}/{total_months} 月份完成")

            # 添加延时避免被封
            time.sleep(1.5)

        logger.info(f"{city_cn} 爬取完成: {success_count}/{total_months} 个月份成功")
        return all_data

    def run_scraper(self, start_year_month="2022-05", end_year_month="2025-7", cities=None):
        """运行爬虫主程序"""
        logger.info("=" * 60)
        logger.info("天气24网站爬虫启动")
        logger.info(f"时间范围: {start_year_month} 到 {end_year_month}")
        logger.info("=" * 60)

        # 生成月份列表
        months = self.get_month_list(start_year_month, end_year_month)
        logger.info(f"需要爬取的月份: {len(months)} 个")

        # 确定要爬取的城市
        target_cities = cities if cities else self.cities
        logger.info(f"目标城市: {list(target_cities.keys())}")

        # 测试网站连接
        test_city = list(target_cities.items())[0]
        test_month = months[0]
        success, _ = self.test_url_access(test_city[1], test_month)
        if not success:
            logger.error("网站连接测试失败，请检查网络或网站状态")
            return

        # 开始爬取
        total_cities = len(target_cities)
        successful_cities = 0

        for i, (city_cn, city_en) in enumerate(target_cities.items(), 1):
            logger.info(f"\n{'='*20} 城市 {i}/{total_cities}: {city_cn} {'='*20}")

            try:
                # 爬取城市数据
                city_data = self.scrape_city_data(city_cn, city_en, months)

                # 保存数据
                if self.save_city_data(city_cn, city_data):
                    successful_cities += 1

            except Exception as e:
                logger.error(f"{city_cn} 爬取过程出错: {e}")
                continue

            # 城市间延时
            if i < total_cities:
                logger.info("等待 3 秒后继续下一个城市...")
                time.sleep(3)

        # 爬取总结
        logger.info("\n" + "=" * 60)
        logger.info("爬取任务完成")
        logger.info(f"成功城市: {successful_cities}/{total_cities}")
        logger.info(f"数据保存目录: {self.data_dir}")
        logger.info("=" * 60)

    def validate_data(self, filepath):
        """验证数据文件"""
        try:
            df = pd.read_csv(filepath, encoding='utf-8-sig')

            logger.info(f"数据验证: {os.path.basename(filepath)}")
            logger.info(f"  总记录数: {len(df)}")
            logger.info(f"  列名: {list(df.columns)}")

            if len(df) > 0:
                logger.info(f"  日期范围: {df['日期'].iloc[0]} 到 {df['日期'].iloc[-1]}")
                logger.info(f"  样例数据: {df.iloc[0].to_dict()}")

            return True

        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return False


def main():
    """主函数 - 使用示例"""
    scraper = Tianqi24WeatherScraper()

    # 配置爬取参数
    start_year_month = "2019-1"  # 开始年月
    end_year_month = "2025-6"    # 结束年月

    # 可以指定特定城市，或者使用None爬取所有城市
    target_cities = {'丽水市': 'lishui'}  # 指定城市
    # target_cities = None  # 爬取所有城市

    try:
        # 运行爬虫
        scraper.run_scraper(
            start_year_month=start_year_month,
            end_year_month=end_year_month,
            cities=target_cities
        )

        # 验证生成的数据文件
        logger.info("\n验证生成的数据文件...")
        data_dir = scraper.data_dir
        for filename in os.listdir(data_dir):
            if filename.endswith('.csv'):
                filepath = os.path.join(data_dir, filename)
                scraper.validate_data(filepath)

    except KeyboardInterrupt:
        logger.info("\n用户中断爬取任务")
    except Exception as e:
        logger.error(f"爬取任务异常: {e}")


def quick_test():
    """快速测试函数 - 爬取单个城市单个月份"""
    scraper = Tianqi24WeatherScraper()

    # 测试温州2023年10月数据
    city_cn = "温州市"
    city_en = "wenzhou"
    year_month = "202310"

    logger.info(f"快速测试: {city_cn} {year_month}")

    # 测试URL访问
    success, html = scraper.test_url_access(city_en, year_month)
    if not success:
        logger.error("URL访问测试失败")
        return

    # 解析数据
    data = scraper.parse_weather_data_improved(html, year_month)

    if data:
        logger.info(f"成功解析 {len(data)} 条数据")

        # 显示前几条数据
        df = pd.DataFrame(data, columns=scraper.columns)
        logger.info("前5条数据:")
        logger.info(df.head().to_string(index=False))

        # 保存测试数据
        test_file = os.path.join(scraper.data_dir, f"test_{city_cn}_{year_month}.csv")
        df.to_csv(test_file, index=False, encoding='utf-8-sig')
        logger.info(f"测试数据已保存: {test_file}")
    else:
        logger.error("数据解析失败")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 运行快速测试
        quick_test()
    else:
        # 运行完整爬虫
        main()
