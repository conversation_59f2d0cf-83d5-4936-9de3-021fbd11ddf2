#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
天气爬虫配置文件
"""

# 爬取配置
SCRAPER_CONFIG = {
    # 时间范围配置
    'start_year_month': '2022-5',  # 开始年月 (YYYY-MM格式)
    'end_year_month': '2025-7',    # 结束年月 (YYYY-MM格式)
    
    # 网络请求配置
    'request_timeout': 15,          # 请求超时时间(秒)
    'retry_count': 3,               # 重试次数
    'delay_between_requests': 1.5,  # 请求间延时(秒)
    'delay_between_cities': 3,      # 城市间延时(秒)
    
    # 数据保存配置
    'data_directory': './weather_data',  # 数据保存目录
    'file_encoding': 'utf-8-sig',        # 文件编码
    'save_progress': True,                # 是否保存进度
    
    # 日志配置
    'log_level': 'INFO',            # 日志级别: DEBUG, INFO, WARNING, ERROR
    'log_to_file': True,            # 是否保存日志到文件
    'log_filename': 'weather_scraper.log',
}

# 浙江省城市配置
ZHEJIANG_CITIES = {
    '杭州市': 'hangzhou',
    '宁波市': 'ningbo', 
    '温州市': 'wenzhou',
    '嘉兴市': 'jiaxing',
    '湖州市': 'huzhou',
    '绍兴市': 'shaoxing',
    '金华市': 'jinhua',
    '衢州市': 'quzhou',
    '舟山市': 'zhoushan',
    '台州市': 'taizhou',
    '丽水市': 'lishui'
}

# 数据字段配置
DATA_COLUMNS = ['日期', '天气状况', '高温', '低温', 'AQI', '风向风力', '降水量']

# 网站配置
WEBSITE_CONFIG = {
    'base_url': 'https://www.tianqi24.com',
    'url_pattern': '{base_url}/{city}/history{year_month}.html',
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
}

# 数据验证配置
VALIDATION_CONFIG = {
    'date_pattern': r'^\d{2}-\d{2}$',           # 日期格式: MM-DD
    'temp_pattern': r'^\d+℃$',                  # 温度格式: 数字+℃
    'aqi_range': (0, 500),                      # AQI合理范围
    'rainfall_range': (0, 1000),               # 降水量合理范围(mm)
    'required_fields': ['日期', '天气状况', '高温', '低温']  # 必需字段
}
