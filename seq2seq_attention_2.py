'''
Enhanced LSTM 预测模型 - 支持多变量时间序列预测
使用 LSTM many-to-many 架构和注意力机制
支持早停、超参数调优、正则化等高级功能
'''
import sys
import csv
import math
import numpy as np
import matplotlib.pyplot as plt
import logging
import json
import os
from datetime import datetime

# Try to import Keras/TensorFlow with fallback
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import backend as K
from tensorflow.keras.models import Sequential, load_model, Model
from tensorflow.keras.layers import LSTM, Dense, Activation, TimeDistributed, Dropout, Lambda, RepeatVector, Input, Reshape, Concatenate, Dot, LayerNormalization, BatchNormalization
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau, CSVLogger
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.regularizers import l1_l2

from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split

from utils import *

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ModelConfig:
    """模型配置类，管理所有超参数"""

    def __init__(self):
        # 数据参数
        self.validate_percent = 0.8
        self.time_step = 96
        self.after_day = 30
        self.target_col_index = 0

        # 模型架构参数
        self.encoder_units = 32
        self.decoder_units = 32
        self.attention_units = 50
        self.dropout_rate = 0.25
        self.recurrent_dropout = 0.25
        self.use_layer_norm = True
        self.use_batch_norm = False

        # 正则化参数
        self.l1_reg = 0.0001
        self.l2_reg = 0.001

        # 训练参数
        self.batch_size = 32
        self.epochs = 100
        self.learning_rate = 0.001
        self.patience = 20
        self.min_delta = 0.0001
        self.monitor_metric = 'val_loss'
        self.reduce_lr_patience = 10
        self.reduce_lr_factor = 0.5

        # 文件路径
        self.data_path = './data/整合天气数据_代理购电.xlsx'
        self.model_save_path = 'model/'
        self.log_path = 'logs/'

    def save_config(self, filepath):
        """保存配置到JSON文件"""
        config_dict = {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)

    def load_config(self, filepath):
        """从JSON文件加载配置"""
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
                for k, v in config_dict.items():
                    setattr(self, k, v)


def TBrain_loss(y_true, y_pred, target_col_index=0):
    """
    自定义损失函数，用于训练模型
    对不同时间步的预测误差赋予不同权重
    
    参数:
        y_true: 真实值，形状为 (batch_size, time_steps, features)
        y_pred: 预测值，形状为 (batch_size, time_steps, features)
    
    返回:
        加权平均损失值
    """
    # 计算第1天的收盘价预测误差
    err_1 = K.mean(K.square(y_true[:, :, target_col_index] - y_pred[:, :, target_col_index]), axis=-1)
    # 计算第2天的收盘价预测误差（未使用）
    # err_2 = K.mean(K.square(y_true[:, 1, 3] - y_pred[:, 1, 3]), axis=-1)
    # # 计算第3天的收盘价预测误差
    # err_3 = K.mean(K.square(y_true[:, 2, 3] - y_pred[:, 2, 3]), axis=-1)
    # # 计算第4天的收盘价预测误差（未使用）
    # err_4 = K.mean(K.square(y_true[:, 3, 3] - y_pred[:, 3, 3]), axis=-1)
    # # 计算第5天的收盘价预测误差
    # err_5 = K.mean(K.square(y_true[:, 4, 3] - y_pred[:, 4, 3]), axis=-1)

    # 返回加权损失：第1天权重50，第3天权重30，第5天权重20
    return ( err_1 )


def load_data(data, time_step=20, after_day=1, validate_percent=0.67):
    """
    加载和预处理数据，将时间序列数据转换为训练和验证集
    
    参数:
        data: 原始时间序列数据，形状为 (samples, features)
        time_step: 输入序列长度，默认20个时间步
        after_day: 预测未来天数，默认1天
        validate_percent: 训练集比例，默认67%
    
    返回:
        x_train: 训练输入数据，形状为 (train_samples, time_step, features)
        y_train: 训练标签数据，形状为 (train_samples, after_day, features)
        x_validate: 验证输入数据，形状为 (validate_samples, time_step, features)
        y_validate: 验证标签数据，形状为 (validate_samples, after_day, features)
    """
    seq_length = time_step + after_day
    result = []
    for index in range(len(data) - seq_length + 1):
        result.append(data[index: index + seq_length])

    result = np.array(result)
    print('total data: ', result.shape)

    train_size = int(len(result) * validate_percent)
    train = result[:train_size, :]
    validate = result[train_size:, :]

    x_train = train[:, :time_step]
    y_train = train[:, time_step:]
    x_validate = validate[:, :time_step]
    y_validate = validate[:, time_step:]

    return [x_train, y_train, x_validate, y_validate]


def softmax(x, axis=1):
    """
    Softmax激活函数
    
    参数:
        x: 输入张量
        axis: 应用softmax归一化的轴，默认为1
    
    返回:
        经过softmax变换的张量
    """
    ndim = K.ndim(x)
    if ndim == 2:
        return K.softmax(x)
    elif ndim > 2:
        e = K.exp(x - K.max(x, axis=axis, keepdims=True))
        s = K.sum(e, axis=axis, keepdims=True)
        return e / s
    else:
        raise ValueError('Cannot apply softmax to a tensor that is 1D')


def one_step_attention(a, s_prev, repeator, concatenator, densor, activator, dotor):
    """
    单步注意力机制计算
    
    参数:
        a: 编码器输出，形状为 (batch_size, time_steps, hidden_size)
        s_prev: 解码器前一个时间步的隐藏状态
        repeator: RepeatVector层，用于重复隐藏状态
        concatenator: Concatenate层，用于连接向量
        densor: Dense层，用于计算注意力分数
        activator: 激活函数层，用于softmax归一化
        dotor: Dot层，用于计算加权上下文向量
    
    返回:
        context: 加权上下文向量
    """
    s_prev = repeator(s_prev)
    concat = concatenator([s_prev, a])
    e = densor(concat)
    alphas = activator(e)
    context = dotor([alphas, a])

    return context


def seq2seq_attention(config, feature_len=1, after_day=1, input_shape=(20, 1), time_step=20):
    """
    构建增强的带有注意力机制的序列到序列模型

    参数:
        config: ModelConfig对象，包含所有配置参数
        feature_len: 特征维度，默认1
        after_day: 预测未来天数，默认1天
        input_shape: 输入形状，默认(20, 1)
        time_step: 时间步长，默认20

    返回:
        model: 构建好的Keras模型
    """
    logger.info(f"构建模型 - 输入形状: {input_shape}, 特征数: {feature_len}, 预测天数: {after_day}")

    # 定义模型输入，形状为 (Tx, feature)
    X = Input(shape=input_shape, name='input_sequence')

    # 初始化输出列表
    all_outputs = []

    # 正则化器
    regularizer = l1_l2(l1=config.l1_reg, l2=config.l2_reg)

    # 编码器：增强的LSTM层
    encoder = LSTM(
        units=config.encoder_units,
        return_state=True,
        return_sequences=True,
        dropout=config.dropout_rate,
        recurrent_dropout=config.recurrent_dropout,
        kernel_regularizer=regularizer,
        name='encoder'
    )

    # 解码器：增强的LSTM层
    decoder = LSTM(
        units=config.decoder_units,
        return_state=True,
        dropout=config.dropout_rate,
        recurrent_dropout=config.recurrent_dropout,
        kernel_regularizer=regularizer,
        name='decoder'
    )

    # 输出层
    decoder_output = Dense(
        units=feature_len,
        activation='linear',
        kernel_regularizer=regularizer,
        name='output'
    )
    model_output = Reshape((1, feature_len))

    # 增强的注意力机制相关层
    repeator = RepeatVector(time_step)
    concatenator = Concatenate(axis=-1)

    # 改进的注意力计算层
    attention_dense = Dense(
        config.attention_units,
        activation="tanh",
        kernel_regularizer=regularizer,
        name='attention_dense'
    )
    attention_score = Dense(
        1,
        activation="linear",
        kernel_regularizer=regularizer,
        name='attention_score'
    )
    activator = Activation(softmax, name='attention_weights')
    dotor = Dot(axes=1)

    # 编码器前向传播
    encoder_outputs, s, c = encoder(X)

    # 可选的层归一化
    if config.use_layer_norm:
        encoder_outputs = LayerNormalization(name='encoder_layer_norm')(encoder_outputs)

    # 可选的批归一化
    if config.use_batch_norm:
        encoder_outputs = BatchNormalization(name='encoder_batch_norm')(encoder_outputs)

    # 解码器循环预测
    for t in range(after_day):
        # 计算增强的注意力上下文
        context = enhanced_attention(
            encoder_outputs, s, repeator, concatenator,
            attention_dense, attention_score, activator, dotor
        )

        # 解码器前向传播
        a, s, c = decoder(context, initial_state=[s, c])

        # 可选的层归一化
        if config.use_layer_norm:
            a = LayerNormalization(name=f'decoder_layer_norm_{t}')(a)

        # 生成输出
        outputs = decoder_output(a)
        outputs = model_output(outputs)
        all_outputs.append(outputs)

    # 连接所有时间步的输出
    all_outputs = Lambda(lambda x: K.concatenate(x, axis=1), name='concat_outputs')(all_outputs)
    model = Model(inputs=X, outputs=all_outputs, name='enhanced_seq2seq_attention')

    logger.info(f"模型构建完成 - 总参数数量: {model.count_params()}")
    return model


def enhanced_attention(a, s_prev, repeator, concatenator, attention_dense, attention_score, activator, dotor):
    """
    增强的单步注意力机制计算

    参数:
        a: 编码器输出，形状为 (batch_size, time_steps, hidden_size)
        s_prev: 解码器前一个时间步的隐藏状态
        repeator: RepeatVector层，用于重复隐藏状态
        concatenator: Concatenate层，用于连接向量
        attention_dense: Dense层，用于注意力特征变换
        attention_score: Dense层，用于计算注意力分数
        activator: 激活函数层，用于softmax归一化
        dotor: Dot层，用于计算加权上下文向量

    返回:
        context: 加权上下文向量
    """
    s_prev = repeator(s_prev)
    concat = concatenator([s_prev, a])

    # 增强的注意力计算
    attention_features = attention_dense(concat)
    e = attention_score(attention_features)
    alphas = activator(e)
    context = dotor([alphas, a])

    return context


def create_callbacks(config, model_name):
    """创建训练回调函数"""
    callbacks = []

    # 早停回调
    early_stopping = EarlyStopping(
        monitor=config.monitor_metric,
        patience=config.patience,
        min_delta=config.min_delta,
        restore_best_weights=True,
        verbose=1
    )
    callbacks.append(early_stopping)

    # 学习率调度
    reduce_lr = ReduceLROnPlateau(
        monitor=config.monitor_metric,
        factor=config.reduce_lr_factor,
        patience=config.reduce_lr_patience,
        min_lr=1e-7,
        verbose=1
    )
    callbacks.append(reduce_lr)

    # 模型检查点
    checkpoint_path = os.path.join(config.model_save_path, f'{model_name}_best.h5')
    os.makedirs(config.model_save_path, exist_ok=True)
    checkpoint = ModelCheckpoint(
        checkpoint_path,
        monitor=config.monitor_metric,
        save_best_only=True,
        save_weights_only=False,
        verbose=1
    )
    callbacks.append(checkpoint)

    # CSV日志记录
    os.makedirs(config.log_path, exist_ok=True)
    csv_logger = CSVLogger(
        os.path.join(config.log_path, f'{model_name}_training.csv'),
        append=True
    )
    callbacks.append(csv_logger)

    return callbacks


def save_evaluation_metrics(model_name, train_loss, train_mae, train_mse,
                           validate_loss, validate_mae, validate_mse, history):
    """
    保存模型评价指标到CSV文件

    参数:
        model_name: 模型名称
        train_loss, train_mae, train_mse: 训练集指标
        validate_loss, validate_mae, validate_mse: 验证集指标
        history: 训练历史对象
    """
    try:
        # 创建输出目录
        output_dir = 'outputs'
        os.makedirs(output_dir, exist_ok=True)

        # 准备评价指标数据
        metrics_data = {
            'Model_Name': model_name,
            'Train_Loss': train_loss,
            'Train_RMSE': math.sqrt(train_loss),
            'Train_MAE': train_mae if train_mae is not None else 'N/A',
            'Train_MSE': train_mse if train_mse is not None else 'N/A',
            'Validation_Loss': validate_loss,
            'Validation_RMSE': math.sqrt(validate_loss),
            'Validation_MAE': validate_mae if validate_mae is not None else 'N/A',
            'Validation_MSE': validate_mse if validate_mse is not None else 'N/A',
            'Final_Train_Loss': history.history['loss'][-1],
            'Final_Val_Loss': history.history['val_loss'][-1],
            'Best_Train_Loss': min(history.history['loss']),
            'Best_Val_Loss': min(history.history['val_loss']),
            'Total_Epochs': len(history.history['loss']),
            'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # 保存到CSV文件
        metrics_file = os.path.join(output_dir, f'{model_name}_evaluation_metrics.csv')

        with open(metrics_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            # 写入标题行
            writer.writerow(['Metric', 'Value'])
            # 写入数据行
            for metric, value in metrics_data.items():
                writer.writerow([metric, value])

        logger.info(f"评价指标已保存到: {metrics_file}")

    except Exception as e:
        logger.error(f"保存评价指标失败: {str(e)}")


def validate_data_quality(data, config):
    """验证数据质量"""
    logger.info("验证数据质量...")

    # 检查数据形状
    if len(data.shape) != 2:
        raise ValueError(f"数据应为2维，实际为{len(data.shape)}维")

    # 检查最小样本数
    min_samples = config.time_step + config.after_day + 50
    if data.shape[0] < min_samples:
        raise ValueError(f"数据样本数不足，需要至少{min_samples}个样本，实际{data.shape[0]}个")

    # 检查数据范围
    data_min, data_max = np.min(data), np.max(data)
    logger.info(f"数据范围: [{data_min:.6f}, {data_max:.6f}]")

    # 检查异常值
    q1, q3 = np.percentile(data, [25, 75])
    iqr = q3 - q1
    outlier_threshold = 3 * iqr
    outliers = np.sum((data < q1 - outlier_threshold) | (data > q3 + outlier_threshold))
    outlier_ratio = outliers / data.size

    logger.info(f"异常值比例: {outlier_ratio:.4f}")
    if outlier_ratio > 0.1:
        logger.warning("异常值比例较高，可能影响模型性能")

    return True


if __name__ == '__main__':
    # 初始化配置
    config = ModelConfig()

    # 获取模型名称（只取文件名，不包含路径）
    model_name = os.path.basename(sys.argv[0]).replace(".py", "")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_name_with_time = f"{model_name}_{timestamp}"

    logger.info(f"开始训练模型: {model_name_with_time}")

    # 保存配置
    config.save_config(f'configs/{model_name_with_time}_config.json')

    # 数据标准化器
    scaler = MinMaxScaler(feature_range=(0, 1))
    output = []  # 存储预测结果


    try:
        # 从Excel文件读取数据，返回数据形状: (Samples, feature)
        logger.info(f"加载数据文件: {config.data_path}")
        data = file_processing(config.data_path)

        # 多变量参数设定
        feature_len = data.shape[1]
        logger.info(f"特征数量: {feature_len}")

        # 验证数据质量
        validate_data_quality(data, config)

        # 数据标准化
        logger.info("开始数据标准化...")
        data = normalize_data(data, scaler, feature_len)
        logger.info("数据标准化完成")

        # 测试数据（最后time_step个时间步）
        x_test = data[-config.time_step:]
        x_test = np.reshape(x_test, (1, x_test.shape[0], x_test.shape[1]))

        # 获取训练和验证数据
        logger.info("分割训练和验证数据...")
        x_train, y_train, x_validate, y_validate = load_data(
            data,
            time_step=config.time_step,
            after_day=config.after_day,
            validate_percent=config.validate_percent
        )

        logger.info(f'训练数据形状: {x_train.shape}, {y_train.shape}')
        logger.info(f'验证数据形状: {x_validate.shape}, {y_validate.shape}')

        # 模型构建
        logger.info("构建模型...")
        input_shape = (config.time_step, feature_len)
        model = seq2seq_attention(
            config,
            feature_len,
            config.after_day,
            input_shape,
            config.time_step
        )

        # 模型编译
        optimizer = Adam(learning_rate=config.learning_rate)
        model.compile(
            loss=lambda y_true, y_pred: TBrain_loss(y_true, y_pred, config.target_col_index),
            optimizer=optimizer,
            metrics=['mae', 'mse']
        )

        logger.info("模型结构:")
        model.summary(print_fn=logger.info)

        # 可选：绘制模型架构
        # plot_model_architecture(model, model_name=model_name_with_time)

        # 创建训练回调
        logger.info("创建训练回调...")
        callbacks = create_callbacks(config, model_name_with_time)

        # 模型训练
        logger.info("开始模型训练...")
        history = model.fit(
            x_train, y_train,
            batch_size=config.batch_size,
            epochs=config.epochs,
            validation_data=(x_validate, y_validate),
            callbacks=callbacks,
            verbose=1
        )

        # 保存最终模型
        model_class_name = model_name_with_time
        save_model(model, model_name=model_class_name)
        logger.info(f"模型已保存: {model_class_name}")

        logger.info('-' * 100)
        # 评估训练集性能
        logger.info("评估模型性能...")
        train_scores = model.evaluate(x_train, y_train, batch_size=config.batch_size, verbose=0)
        if isinstance(train_scores, list):
            train_loss = train_scores[0]
            train_mae = train_scores[1] if len(train_scores) > 1 else None
            train_mse = train_scores[2] if len(train_scores) > 2 else None
            logger.info(f'训练集损失: {train_loss:.8f} (RMSE: {math.sqrt(train_loss):.8f})')
            if len(train_scores) > 1:
                logger.info(f'训练集MAE: {train_mae:.8f}, MSE: {train_mse:.8f}')
        else:
            train_loss = train_scores
            train_mae = None
            train_mse = None
            logger.info(f'训练集损失: {train_loss:.8f} (RMSE: {math.sqrt(train_loss):.8f})')

        # 评估验证集性能
        validate_scores = model.evaluate(x_validate, y_validate, batch_size=config.batch_size, verbose=0)
        if isinstance(validate_scores, list):
            validate_loss = validate_scores[0]
            validate_mae = validate_scores[1] if len(validate_scores) > 1 else None
            validate_mse = validate_scores[2] if len(validate_scores) > 2 else None
            logger.info(f'验证集损失: {validate_loss:.8f} (RMSE: {math.sqrt(validate_loss):.8f})')
            if len(validate_scores) > 1:
                logger.info(f'验证集MAE: {validate_mae:.8f}, MSE: {validate_mse:.8f}')
        else:
            validate_loss = validate_scores
            validate_mae = None
            validate_mse = None
            logger.info(f'验证集损失: {validate_loss:.8f} (RMSE: {math.sqrt(validate_loss):.8f})')

        # 保存评价指标到CSV文件
        save_evaluation_metrics(
            model_name_with_time,
            train_loss, train_mae, train_mse,
            validate_loss, validate_mae, validate_mse,
            history
        )

    except Exception as e:
        logger.error(f"训练过程失败: {str(e)}")
        raise

    try:
        # 模型预测
        logger.info("开始模型预测...")
        train_predict = model.predict(x_train, batch_size=config.batch_size)
        validate_predict = model.predict(x_validate, batch_size=config.batch_size)
        test_predict = model.predict(x_test, batch_size=config.batch_size)

        # 将预测数据反标准化为原始数据规模
        logger.info("反标准化预测结果...")
        train_predict = inverse_normalize_data(train_predict, scaler)
        y_train = inverse_normalize_data(y_train, scaler)
        validate_predict = inverse_normalize_data(validate_predict, scaler)
        y_validate = inverse_normalize_data(y_validate, scaler)
        test_predict = inverse_normalize_data(test_predict, scaler)

        # 提取预测结果：验证集最后一天的真实值 + 测试集未来预测值
        ans = np.append(
            y_validate[-1, -1, config.target_col_index],
            test_predict[-1, 0:config.after_day, config.target_col_index]
        )
        output.append(ans)
        logger.info(f"预测结果: {ans}")

        # 绘制预测结果图（保存到 images/result）
        logger.info("生成预测结果图...")
        file_name = 'result_' + model_name_with_time
        plot_predict(
            y_validate,
            validate_predict,
            file_name=file_name,
            after_day=config.after_day,
            target_col_index=config.target_col_index
        )

        # 绘制损失曲线图（保存到 images/loss）
        logger.info("生成损失曲线图...")
        file_name = 'loss_' + model_name_with_time
        plot_loss(history, file_name)

        # 将所有预测结果转换为数组并生成输出文件
        output = np.array(output)
        logger.info(f"最终输出形状: {output.shape}")
        generate_output(output, model_name=model_name_with_time)

        logger.info("训练和预测完成！")

    except Exception as e:
        logger.error(f"预测过程失败: {str(e)}")
        raise
