#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日天气数据转月度数据转换器
功能：
1. 将浙江省聚合天气数据从日数据转换为月度数据
2. 对天气状况、风向风力等非数值数据进行量化处理
3. 计算月度统计指标（平均值、最大值、最小值、总和等）
"""

import pandas as pd
import numpy as np
import os
import logging
from datetime import datetime
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('daily_to_monthly.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class WeatherDataQuantifier:
    """天气数据量化器"""
    
    def __init__(self):
        """初始化量化器"""
        # 天气状况量化映射
        self.weather_condition_map = {
            '晴': 1.0,
            '多云': 0.7,
            '阴': 0.4,
            '小雨': 0.2,
            '中雨': 0.1,
            '大雨': 0.05,
            '暴雨': 0.02,
            '雪': 0.1,
            '雾': 0.3,
            '霾': 0.2,
            '未知': 0.5  # 中性值
        }
        
        # 风力等级量化映射
        self.wind_force_map = {
            '无风': 0,
            '1级': 1,
            '2级': 2,
            '3级': 3,
            '4级': 4,
            '5级': 5,
            '6级': 6,
            '7级': 7,
            '8级': 8,
            '9级': 9,
            '10级': 10,
            '11级': 11,
            '12级': 12
        }
        
        # 风向量化映射（基于方位角度）
        self.wind_direction_map = {
            '北风': 0,
            '东北风': 45,
            '东风': 90,
            '东南风': 135,
            '南风': 180,
            '西南风': 225,
            '西风': 270,
            '西北风': 315,
            '无风': -1  # 特殊值表示无风
        }

    def quantify_weather_condition(self, condition_str):
        """
        量化天气状况
        
        Args:
            condition_str (str): 天气状况字符串
            
        Returns:
            float: 量化后的天气指数（0-1，1表示最好的天气）
        """
        if pd.isna(condition_str) or condition_str == '':
            return 0.5
        
        # 处理复合天气状况（如"多云/晴"）
        conditions = re.split(r'[/、]', str(condition_str))
        scores = []
        
        for condition in conditions:
            condition = condition.strip()
            # 寻找最匹配的天气状况
            best_match = None
            for key in self.weather_condition_map:
                if key in condition:
                    best_match = key
                    break
            
            if best_match:
                scores.append(self.weather_condition_map[best_match])
            else:
                scores.append(0.5)  # 未知情况使用中性值
        
        # 返回平均值
        return np.mean(scores)

    def quantify_wind_info(self, wind_str):
        """
        量化风向风力信息
        
        Args:
            wind_str (str): 风向风力字符串（如"东南风2级"）
            
        Returns:
            tuple: (风向角度, 风力等级)
        """
        if pd.isna(wind_str) or wind_str == '':
            return -1, 0
        
        wind_str = str(wind_str).strip()
        
        # 提取风力等级
        wind_force = 0
        for level, value in self.wind_force_map.items():
            if level in wind_str:
                wind_force = value
                break
        
        # 提取风向
        wind_direction = -1
        for direction, angle in self.wind_direction_map.items():
            if direction in wind_str:
                wind_direction = angle
                break
        
        return wind_direction, wind_force


class DailyToMonthlyConverter:
    """日数据转月度数据转换器"""
    
    def __init__(self):
        """初始化转换器"""
        self.daily_data = None
        self.monthly_data = None
        self.quantifier = WeatherDataQuantifier()

    def load_daily_data(self, file_path):
        """
        加载日天气数据

        Args:
            file_path (str): 日数据文件路径

        Returns:
            bool: 加载是否成功
        """
        try:
            # 尝试不同编码读取
            try:
                self.daily_data = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                self.daily_data = pd.read_csv(file_path, encoding='gbk')
                logger.info("使用GBK编码读取数据")

            logger.info(f"成功加载日数据: {file_path}")
            logger.info(f"数据形状: {self.daily_data.shape}")
            logger.info(f"列名: {list(self.daily_data.columns)}")

            # 处理日期列 - 支持多种日期格式
            try:
                self.daily_data['日期'] = pd.to_datetime(self.daily_data['日期'])
            except:
                # 如果直接转换失败，尝试处理特殊格式
                self.daily_data['日期'] = pd.to_datetime(self.daily_data['日期'], format='%Y/%m/%d')

            # 添加年月列用于分组
            self.daily_data['年月'] = self.daily_data['日期'].dt.to_period('M')

            # 处理温度数据 - 提取数值
            self.daily_data['高温_数值'] = self.daily_data['高温'].str.extract(r'(\d+)').astype(float)
            self.daily_data['低温_数值'] = self.daily_data['低温'].str.extract(r'(\d+)').astype(float)

            logger.info(f"日期范围: {self.daily_data['日期'].min()} 到 {self.daily_data['日期'].max()}")
            logger.info(f"温度范围: 高温 {self.daily_data['高温_数值'].min()}-{self.daily_data['高温_数值'].max()}℃, 低温 {self.daily_data['低温_数值'].min()}-{self.daily_data['低温_数值'].max()}℃")

            return True

        except Exception as e:
            logger.error(f"加载日数据失败: {str(e)}")
            return False

    def quantify_categorical_data(self):
        """
        量化分类数据
        """
        logger.info("开始量化分类数据...")
        
        # 量化天气状况
        self.daily_data['天气指数'] = self.daily_data['天气状况'].apply(
            self.quantifier.quantify_weather_condition
        )
        
        # 量化风向风力
        wind_info = self.daily_data['风向风力'].apply(
            self.quantifier.quantify_wind_info
        )
        self.daily_data['风向角度'] = [info[0] for info in wind_info]
        self.daily_data['风力等级'] = [info[1] for info in wind_info]
        
        logger.info("分类数据量化完成")

    def convert_to_monthly(self):
        """
        转换为月度数据

        Returns:
            bool: 转换是否成功
        """
        if self.daily_data is None:
            logger.error("日数据未加载")
            return False

        try:
            logger.info("开始转换为月度数据...")

            # 先进行数据量化
            self.quantify_categorical_data()

            # 构建聚合字典 - 根据实际存在的列
            agg_dict = {
                # 温度数据
                '高温_数值': ['mean', 'max', 'min', 'std'],
                '低温_数值': ['mean', 'max', 'min', 'std'],

                # 量化后的天气指数
                '天气指数': ['mean', 'min'],

                # 风力风向
                '风力等级': ['mean', 'max'],
                '风向角度': ['mean'],  # 风向的平均角度

                # 计算一些额外指标
                '日期': ['count']  # 每月天数
            }

            # 按年月分组聚合
            monthly_agg = self.daily_data.groupby('年月').agg(agg_dict).round(2)

            # 重命名列
            monthly_agg.columns = [
                '平均高温', '最高温度', '最低高温', '高温标准差',
                '平均低温', '最高低温', '最低低温', '低温标准差',
                '平均天气指数', '最差天气指数',
                '平均风力等级', '最大风力等级',
                '平均风向角度',
                '月天数'
            ]

            # 重置索引
            self.monthly_data = monthly_agg.reset_index()

            # 添加年份和月份列
            self.monthly_data['年份'] = self.monthly_data['年月'].dt.year
            self.monthly_data['月份'] = self.monthly_data['年月'].dt.month

            # 计算一些衍生指标
            self.monthly_data['温差'] = (self.monthly_data['平均高温'] -
                                      self.monthly_data['平均低温']).round(2)

            # 计算天气状况统计
            weather_stats = self.calculate_weather_statistics()
            if weather_stats is not None:
                self.monthly_data = pd.concat([self.monthly_data, weather_stats], axis=1)

            # 将年月转换为字符串格式
            self.monthly_data['年月'] = self.monthly_data['年月'].astype(str)

            logger.info(f"月度数据转换完成，共 {len(self.monthly_data)} 个月的数据")
            logger.info(f"月度数据列名: {list(self.monthly_data.columns)}")

            return True

        except Exception as e:
            logger.error(f"转换为月度数据失败: {str(e)}")
            return False

    def calculate_weather_statistics(self):
        """
        计算天气状况统计

        Returns:
            DataFrame: 天气统计数据
        """
        try:
            # 按年月和天气状况分组统计
            weather_counts = self.daily_data.groupby(['年月', '天气状况']).size().unstack(fill_value=0)

            # 计算各种天气的天数和比例
            weather_stats = pd.DataFrame(index=weather_counts.index)

            # 计算主要天气状况的天数
            for weather in ['晴', '多云', '阴', '小雨', '中雨', '大雨']:
                if weather in weather_counts.columns:
                    weather_stats[f'{weather}天数'] = weather_counts[weather]
                    weather_stats[f'{weather}比例'] = (weather_counts[weather] /
                                                   weather_counts.sum(axis=1)).round(3)
                else:
                    weather_stats[f'{weather}天数'] = 0
                    weather_stats[f'{weather}比例'] = 0.0

            # 计算降水天数（包含雨的天气）
            rain_columns = [col for col in weather_counts.columns if '雨' in col]
            if rain_columns:
                weather_stats['降水天数'] = weather_counts[rain_columns].sum(axis=1)
                weather_stats['降水频率'] = (weather_stats['降水天数'] /
                                        weather_counts.sum(axis=1)).round(3)
            else:
                weather_stats['降水天数'] = 0
                weather_stats['降水频率'] = 0.0

            return weather_stats.reset_index(drop=True)

        except Exception as e:
            logger.warning(f"计算天气统计失败: {e}")
            return None

    def save_monthly_data(self, output_dir='./data', city_name='丽水市'):
        """
        保存月度数据

        Args:
            output_dir (str): 输出目录
            city_name (str): 城市名称

        Returns:
            tuple: (Excel文件路径, CSV文件路径) 或 (None, None) 如果失败
        """
        if self.monthly_data is None:
            logger.error("月度数据未生成，请先运行convert_to_monthly()")
            return None, None

        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_path = os.path.join(output_dir, f'{city_name}_月度天气数据_{timestamp}.xlsx')
            csv_path = os.path.join(output_dir, f'{city_name}_月度天气数据_{timestamp}.csv')

            # 保存为Excel文件
            try:
                self.monthly_data.to_excel(excel_path, index=False, engine='openpyxl')
                logger.info(f"月度数据已保存为Excel文件: {excel_path}")
            except ImportError:
                logger.warning("openpyxl未安装，跳过Excel文件保存")
                excel_path = None

            # 保存为CSV文件
            self.monthly_data.to_csv(csv_path, index=False, encoding='utf-8-sig')
            logger.info(f"月度数据已保存为CSV文件: {csv_path}")

            return excel_path, csv_path

        except Exception as e:
            logger.error(f"保存月度数据失败: {str(e)}")
            return None, None

    def get_monthly_summary(self):
        """
        获取月度数据摘要
        
        Returns:
            dict: 数据摘要
        """
        if self.monthly_data is None:
            return None
        
        summary = {
            '月度记录数': len(self.monthly_data),
            '时间范围': f"{self.monthly_data['年月'].min()} 到 {self.monthly_data['年月'].max()}",
            '列数': len(self.monthly_data.columns),
            '列名': list(self.monthly_data.columns),
            '数值统计': self.monthly_data.select_dtypes(include=[np.number]).describe().round(2).to_dict()
        }
        
        return summary


def main():
    """
    主函数：执行日数据转月度数据转换
    """
    logger.info("开始执行丽水市日天气数据转月度数据任务...")
    logger.info("=" * 60)

    # 初始化转换器
    converter = DailyToMonthlyConverter()

    # 文件路径
    input_file = './weather_data/丽水市_2022_weather_data.csv'

    # 步骤1: 加载日数据
    logger.info("步骤1: 加载丽水市日天气数据")
    if not converter.load_daily_data(input_file):
        logger.error("加载日数据失败，程序终止")
        return False

    # 步骤2: 转换为月度数据
    logger.info("步骤2: 转换为月度数据")
    if not converter.convert_to_monthly():
        logger.error("转换为月度数据失败，程序终止")
        return False

    # 步骤3: 保存月度数据
    logger.info("步骤3: 保存月度数据")
    excel_path, csv_path = converter.save_monthly_data(
        output_dir='../data',
        city_name='丽水市'
    )

    if csv_path:  # 只要CSV保存成功就算成功
        logger.info("丽水市日数据转月度数据任务完成！")

        # 显示数据摘要
        summary = converter.get_monthly_summary()
        if summary:
            logger.info("月度数据摘要:")
            for key, value in summary.items():
                if key != '数值统计':
                    logger.info(f"  {key}: {value}")

        # 显示前几行数据
        logger.info("前5行月度数据:")
        print(converter.monthly_data.head().to_string(index=False))

        logger.info("=" * 60)
        return True
    else:
        logger.error("保存月度数据失败，程序终止")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("✓ 日天气数据转月度数据完成！")
    else:
        print("✗ 日天气数据转月度数据失败！")
