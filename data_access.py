import pandas as pd
from datetime import datetime, timedelta

# 读取外贸出口负荷表格
power_path = 'data/外贸出口负荷.xlsx'
power_df = pd.read_excel(power_path)

# 读取浙江杭州天气表格
weather_path = 'data/浙江杭州杭州-2025-06.xlsx'
weather_df = pd.read_excel(weather_path)

# 1. 为电力数据补全日期
start_date = datetime(2025, 6, 1)
dates = []
for day in range(30):
    date = start_date + timedelta(days=day)
    for i in range(96):  # 96个15分钟
        dates.append(date.strftime('%Y-%m-%d'))
power_df['date_full'] = dates

# 2. 处理电力数据的时间列，保留小时和分钟
power_df['hour'] = power_df['date'].apply(
    lambda x: int(str(x).split(':')[0])
)
power_df['minute'] = power_df['date'].apply(
    lambda x: int(str(x).split(':')[1])
)

# 3. 处理天气数据，提取日期和小时
weather_df['date_full'] = weather_df['时间'].apply(
    lambda x: str(x).split(' ')[0]
)
weather_df['hour'] = weather_df['时间'].apply(
    lambda x: int(str(x).split(' ')[1].split(':')[0])
)

# 4. 选择需要合并的天气字段
weather_fields = [
    '降水量(mm)', '风力(级)', '风速(km/h)', '气压(hPa)', '湿度(%)', '空气质量',
    '能见度(km)', '云量%', '露点℃', 'PM2.5', 'SO2', 'NO2', 'CO', 'O3', 'PM10'
]
weather_merge = weather_df[['date_full', 'hour'] + weather_fields]

# 5. 合并：电力数据的每15分钟，取对应date_full和hour的天气数据
merged = pd.merge(
    power_df, weather_merge, on=['date_full', 'hour'], how='left'
)

# 6. 可选：输出前几行查看
print(merged.head(20))

# 7. 保存整合后的数据
merged.to_excel('data/merged_power_weather.xlsx', index=False)
merged.to_csv('data/merged_power_weather.csv', index=False)
