#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
李娟娟代理购电数据与浙江省聚合天气数据整合模块
功能：
1. 加载李娟娟代理购电.xlsx文件
2. 加载浙江省聚合天气数据.csv文件
3. 按日期整合两个数据集
4. 保存整合后的数据为Excel和CSV格式
"""

import pandas as pd
import numpy as np
import os
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('power_weather_integration.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PowerWeatherIntegrator:
    """代理购电数据与天气数据整合器"""

    def __init__(self):
        """初始化整合器"""
        self.power_data = None
        self.weather_data = None
        self.integrated_data = None

    def load_power_data(self, power_file_path):
        """
        加载代理购电数据

        Args:
            power_file_path (str): 代理购电Excel文件路径

        Returns:
            bool: 加载是否成功
        """
        try:
            # 读取Excel文件
            self.power_data = pd.read_excel(power_file_path, engine='openpyxl')
            
            logger.info(f"成功加载代理购电数据: {power_file_path}")
            logger.info(f"数据形状: {self.power_data.shape}")
            logger.info(f"列名: {list(self.power_data.columns)}")
            
            # 处理日期列
            if '日期' in self.power_data.columns:
                # 检查日期格式，如果是数字格式（如20220501），需要转换
                first_date = str(self.power_data['日期'].iloc[0])
                if first_date.isdigit() and len(first_date) == 8:
                    # 格式为YYYYMMDD，需要转换为标准日期格式
                    logger.info("检测到YYYYMMDD格式的日期，正在转换...")
                    self.power_data['日期'] = pd.to_datetime(
                        self.power_data['日期'].astype(str), 
                        format='%Y%m%d', 
                        errors='coerce'
                    )
                else:
                    # 尝试直接转换
                    self.power_data['日期'] = pd.to_datetime(
                        self.power_data['日期'], 
                        errors='coerce'
                    )
                
                # 检查转换结果
                invalid_dates = self.power_data['日期'].isna().sum()
                if invalid_dates > 0:
                    logger.warning(f"发现 {invalid_dates} 个无效日期")
                
                logger.info(f"日期范围: {self.power_data['日期'].min()} 到 {self.power_data['日期'].max()}")
            else:
                logger.error("未找到日期列")
                return False
            
            return True

        except Exception as e:
            logger.error(f"加载代理购电数据失败: {str(e)}")
            return False

    def load_weather_data(self, weather_file_path):
        """
        加载天气数据

        Args:
            weather_file_path (str): 天气数据CSV文件路径

        Returns:
            bool: 加载是否成功
        """
        try:
            # 尝试UTF-8编码读取
            try:
                self.weather_data = pd.read_csv(weather_file_path, encoding='utf-8')
            except UnicodeDecodeError:
                # 如果UTF-8失败，尝试GBK编码
                self.weather_data = pd.read_csv(weather_file_path, encoding='gbk')
                logger.info("使用GBK编码读取天气数据")

            logger.info(f"成功加载天气数据: {weather_file_path}")
            logger.info(f"数据形状: {self.weather_data.shape}")
            logger.info(f"列名: {list(self.weather_data.columns)}")

            # 处理日期列
            if '日期' in self.weather_data.columns:
                self.weather_data['日期'] = pd.to_datetime(
                    self.weather_data['日期'], 
                    errors='coerce'
                )
                
                # 检查转换结果
                invalid_dates = self.weather_data['日期'].isna().sum()
                if invalid_dates > 0:
                    logger.warning(f"发现 {invalid_dates} 个无效日期")
                
                logger.info(f"日期范围: {self.weather_data['日期'].min()} 到 {self.weather_data['日期'].max()}")
            else:
                logger.error("未找到日期列")
                return False

            return True

        except Exception as e:
            logger.error(f"加载天气数据失败: {str(e)}")
            return False

    def integrate_data(self, merge_strategy='inner'):
        """
        整合代理购电数据和天气数据

        Args:
            merge_strategy (str): 合并策略
                - 'inner': 只保留两个数据集都有的日期
                - 'left': 保留代理购电数据的所有日期
                - 'outer': 保留所有日期

        Returns:
            bool: 整合是否成功
        """
        if self.power_data is None:
            logger.error("代理购电数据未加载")
            return False

        if self.weather_data is None:
            logger.error("天气数据未加载")
            return False

        try:
            # 移除日期为NaN的行
            power_clean = self.power_data.dropna(subset=['日期']).copy()
            weather_clean = self.weather_data.dropna(subset=['日期']).copy()

            logger.info(f"代理购电数据有效记录: {len(power_clean)} 条")
            logger.info(f"天气数据有效记录: {len(weather_clean)} 条")

            # 按日期合并数据
            self.integrated_data = pd.merge(
                power_clean, 
                weather_clean, 
                on='日期', 
                how=merge_strategy
            )

            # 按日期排序
            self.integrated_data = self.integrated_data.sort_values('日期').reset_index(drop=True)

            logger.info(f"数据整合完成，使用 {merge_strategy} 策略")
            logger.info(f"整合后记录数: {len(self.integrated_data)} 条")
            logger.info(f"整合后列名: {list(self.integrated_data.columns)}")

            # 显示日期范围
            if len(self.integrated_data) > 0:
                logger.info(f"整合后日期范围: {self.integrated_data['日期'].min()} 到 {self.integrated_data['日期'].max()}")

            return True

        except Exception as e:
            logger.error(f"数据整合失败: {str(e)}")
            return False

    def save_integrated_data(self, output_dir='./data'):
        """
        保存整合后的数据

        Args:
            output_dir (str): 输出目录

        Returns:
            tuple: (Excel文件路径, CSV文件路径) 或 (None, None) 如果失败
        """
        if self.integrated_data is None:
            logger.error("没有整合数据可保存，请先运行integrate_data()")
            return None, None

        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_path = os.path.join(output_dir, f'整合天气数据_代理购电_{timestamp}.xlsx')
            csv_path = os.path.join(output_dir, f'整合天气数据_代理购电_{timestamp}.csv')

            # 保存为Excel文件
            self.integrated_data.to_excel(excel_path, index=False, engine='openpyxl')
            logger.info(f"整合数据已保存为Excel文件: {excel_path}")

            # 保存为CSV文件（使用UTF-8编码，添加BOM以确保中文正确显示）
            self.integrated_data.to_csv(csv_path, index=False, encoding='utf-8-sig')
            logger.info(f"整合数据已保存为CSV文件: {csv_path}")

            return excel_path, csv_path

        except Exception as e:
            logger.error(f"保存整合数据失败: {str(e)}")
            return None, None

    def get_data_summary(self):
        """
        获取数据摘要信息

        Returns:
            dict: 数据摘要
        """
        if self.integrated_data is None:
            return None

        summary = {
            '总记录数': len(self.integrated_data),
            '日期范围': f"{self.integrated_data['日期'].min()} 到 {self.integrated_data['日期'].max()}",
            '列数': len(self.integrated_data.columns),
            '列名': list(self.integrated_data.columns),
            '缺失值统计': self.integrated_data.isnull().sum().to_dict()
        }

        return summary


def main():
    """
    主函数：执行代理购电数据与天气数据整合
    """
    logger.info("开始执行代理购电数据与天气数据整合任务...")
    logger.info("=" * 60)

    # 初始化整合器
    integrator = PowerWeatherIntegrator()

    # 文件路径（相对于项目根目录）
    power_file = './data/龙泉代理购电.xlsx'
    weather_file = './weather_data/浙江省聚合天气数据.csv'

    # 步骤1: 加载代理购电数据
    logger.info("步骤1: 加载代理购电数据")
    if not integrator.load_power_data(power_file):
        logger.error("加载代理购电数据失败，程序终止")
        return False

    # 步骤2: 加载天气数据
    logger.info("步骤2: 加载天气数据")
    if not integrator.load_weather_data(weather_file):
        logger.error("加载天气数据失败，程序终止")
        return False

    # 步骤3: 整合数据
    logger.info("步骤3: 整合数据")
    if not integrator.integrate_data(merge_strategy='inner'):
        logger.error("数据整合失败，程序终止")
        return False

    # 步骤4: 保存整合后的数据
    logger.info("步骤4: 保存整合后的数据")
    excel_path, csv_path = integrator.save_integrated_data()
    
    if excel_path and csv_path:
        logger.info("数据整合任务完成！")
        
        # 显示数据摘要
        summary = integrator.get_data_summary()
        if summary:
            logger.info("数据摘要:")
            for key, value in summary.items():
                if key != '缺失值统计':
                    logger.info(f"  {key}: {value}")
        
        logger.info("=" * 60)
        return True
    else:
        logger.error("保存整合数据失败，程序终止")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("✓ 代理购电数据与天气数据整合完成！")
    else:
        print("✗ 代理购电数据与天气数据整合失败！")
