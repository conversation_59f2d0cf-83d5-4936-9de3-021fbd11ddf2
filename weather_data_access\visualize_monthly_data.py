#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
月度天气数据可视化脚本
功能：
1. 读取月度天气数据
2. 生成各种趋势图表
3. 保存可视化结果
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import os
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_latest_monthly_data(data_dir='../data'):
    """
    加载最新的月度数据文件
    
    Args:
        data_dir (str): 数据目录
        
    Returns:
        DataFrame: 月度数据
    """
    # 查找最新的月度数据文件
    import glob
    pattern = os.path.join(data_dir, '浙江省月度天气数据_*.csv')
    files = glob.glob(pattern)
    
    if not files:
        raise FileNotFoundError("未找到月度天气数据文件")
    
    # 选择最新的文件
    latest_file = max(files, key=os.path.getctime)
    print(f"加载数据文件: {latest_file}")
    
    df = pd.read_csv(latest_file)
    df['年月_日期'] = pd.to_datetime(df['年月'])
    
    return df

def create_temperature_trend(df, save_dir='../images'):
    """
    创建温度趋势图
    """
    plt.figure(figsize=(15, 8))
    
    # 创建子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 温度趋势
    ax1.plot(df['年月_日期'], df['平均高温'], 'r-o', label='平均高温', linewidth=2, markersize=4)
    ax1.plot(df['年月_日期'], df['平均低温'], 'b-o', label='平均低温', linewidth=2, markersize=4)
    ax1.fill_between(df['年月_日期'], df['平均高温'], df['平均低温'], alpha=0.3, color='gray', label='温差范围')
    
    ax1.set_title('浙江省月度温度趋势', fontsize=16, fontweight='bold')
    ax1.set_ylabel('温度 (℃)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 温差趋势
    ax2.plot(df['年月_日期'], df['温差'], 'g-o', label='月平均温差', linewidth=2, markersize=4)
    ax2.set_title('浙江省月度温差趋势', fontsize=16, fontweight='bold')
    ax2.set_xlabel('年月', fontsize=12)
    ax2.set_ylabel('温差 (℃)', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, '月度温度趋势.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print("温度趋势图已保存")

def create_aqi_precipitation_trend(df, save_dir='../images'):
    """
    创建AQI和降水量趋势图
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # AQI趋势
    ax1.plot(df['年月_日期'], df['平均AQI'], 'purple', linewidth=2, marker='o', markersize=4)
    ax1.fill_between(df['年月_日期'], df['最低AQI'], df['最高AQI'], alpha=0.3, color='purple', label='AQI范围')
    ax1.set_title('浙江省月度空气质量指数(AQI)趋势', fontsize=16, fontweight='bold')
    ax1.set_ylabel('AQI', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 降水量趋势
    bars = ax2.bar(df['年月_日期'], df['总降水量'], alpha=0.7, color='skyblue', label='月总降水量')
    ax2.plot(df['年月_日期'], df['降水天数'], 'r-o', label='降水天数', linewidth=2, markersize=4)
    
    # 添加第二个y轴显示降水天数
    ax2_twin = ax2.twinx()
    ax2_twin.plot(df['年月_日期'], df['降水天数'], 'r-o', linewidth=2, markersize=4)
    ax2_twin.set_ylabel('降水天数', fontsize=12, color='red')
    
    ax2.set_title('浙江省月度降水量和降水天数趋势', fontsize=16, fontweight='bold')
    ax2.set_xlabel('年月', fontsize=12)
    ax2.set_ylabel('降水量 (mm)', fontsize=12)
    ax2.legend(loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, '月度AQI和降水趋势.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print("AQI和降水趋势图已保存")

def create_weather_index_trend(df, save_dir='../images'):
    """
    创建天气指数和风力趋势图
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 天气指数趋势
    ax1.plot(df['年月_日期'], df['平均天气指数'], 'orange', linewidth=2, marker='o', markersize=4, label='平均天气指数')
    ax1.fill_between(df['年月_日期'], df['最差天气指数'], df['平均天气指数'], alpha=0.3, color='orange')
    ax1.set_title('浙江省月度天气指数趋势 (1=最好天气, 0=最差天气)', fontsize=16, fontweight='bold')
    ax1.set_ylabel('天气指数', fontsize=12)
    ax1.set_ylim(0, 1)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 风力趋势
    ax2.plot(df['年月_日期'], df['平均风力等级'], 'brown', linewidth=2, marker='o', markersize=4, label='平均风力等级')
    ax2.plot(df['年月_日期'], df['最大风力等级'], 'red', linewidth=2, marker='s', markersize=4, label='最大风力等级')
    ax2.set_title('浙江省月度风力等级趋势', fontsize=16, fontweight='bold')
    ax2.set_xlabel('年月', fontsize=12)
    ax2.set_ylabel('风力等级', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, '月度天气指数和风力趋势.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print("天气指数和风力趋势图已保存")

def create_seasonal_analysis(df, save_dir='../images'):
    """
    创建季节性分析图
    """
    # 添加季节信息
    df['季节'] = df['月份'].map({
        12: '冬季', 1: '冬季', 2: '冬季',
        3: '春季', 4: '春季', 5: '春季',
        6: '夏季', 7: '夏季', 8: '夏季',
        9: '秋季', 10: '秋季', 11: '秋季'
    })
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 季节温度分布
    seasons = ['春季', '夏季', '秋季', '冬季']
    colors = ['green', 'red', 'orange', 'blue']
    
    for i, season in enumerate(seasons):
        season_data = df[df['季节'] == season]
        ax1.scatter(season_data['平均高温'], season_data['平均低温'], 
                   c=colors[i], label=season, alpha=0.7, s=60)
    
    ax1.set_title('季节温度分布', fontsize=14, fontweight='bold')
    ax1.set_xlabel('平均高温 (℃)')
    ax1.set_ylabel('平均低温 (℃)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 季节AQI箱线图
    season_aqi_data = [df[df['季节'] == season]['平均AQI'].values for season in seasons]
    bp1 = ax2.boxplot(season_aqi_data, labels=seasons, patch_artist=True)
    for patch, color in zip(bp1['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    ax2.set_title('季节AQI分布', fontsize=14, fontweight='bold')
    ax2.set_ylabel('平均AQI')
    ax2.grid(True, alpha=0.3)
    
    # 季节降水量箱线图
    season_rain_data = [df[df['季节'] == season]['总降水量'].values for season in seasons]
    bp2 = ax3.boxplot(season_rain_data, labels=seasons, patch_artist=True)
    for patch, color in zip(bp2['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    ax3.set_title('季节降水量分布', fontsize=14, fontweight='bold')
    ax3.set_ylabel('总降水量 (mm)')
    ax3.grid(True, alpha=0.3)
    
    # 季节天气指数箱线图
    season_weather_data = [df[df['季节'] == season]['平均天气指数'].values for season in seasons]
    bp3 = ax4.boxplot(season_weather_data, labels=seasons, patch_artist=True)
    for patch, color in zip(bp3['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    ax4.set_title('季节天气指数分布', fontsize=14, fontweight='bold')
    ax4.set_ylabel('平均天气指数')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, '季节性分析.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print("季节性分析图已保存")

def main():
    """
    主函数：生成所有可视化图表
    """
    try:
        print("开始生成月度天气数据可视化图表...")
        print("=" * 50)
        
        # 加载数据
        df = load_latest_monthly_data()
        print(f"数据加载完成，共 {len(df)} 个月的数据")
        
        # 生成各种图表
        create_temperature_trend(df)
        create_aqi_precipitation_trend(df)
        create_weather_index_trend(df)
        create_seasonal_analysis(df)
        
        print("=" * 50)
        print("✓ 所有可视化图表生成完成！")
        print("图表保存在 ../images/ 目录下")
        
    except Exception as e:
        print(f"✗ 生成可视化图表失败: {str(e)}")

if __name__ == "__main__":
    main()
