#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行天气数据处理的主脚本
"""

import sys
import os

# 添加weather_data_access模块到路径
sys.path.append('weather_data_access')

def main():
    """运行主要的数据处理流程"""
    try:
        # 导入处理模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("weather_processor", "weather_data_access/1.py")
        weather_processor = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(weather_processor)

        process_main = weather_processor.main
        
        print("开始执行天气数据处理...")
        result = process_main()
        
        if result:
            print("✓ 数据处理完成！")
        else:
            print("✗ 数据处理失败！")
            
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
        print("请确保weather_data_access模块正确安装")
    except Exception as e:
        print(f"✗ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
