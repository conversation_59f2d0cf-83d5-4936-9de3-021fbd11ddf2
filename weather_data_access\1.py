#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
浙江省天气数据聚合处理模块
功能：
1. 聚合浙江省各城市天气数据
2. 整合天气数据与Excel文件
"""

import pandas as pd
import numpy as np
import os
import logging
import glob

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('weather_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ZhejiangWeatherAggregator:
    """浙江省天气数据聚合器"""

    def __init__(self, weather_data_dir='weather_data'):
        """
        初始化聚合器

        Args:
            weather_data_dir (str): 天气数据目录路径
        """
        self.weather_data_dir = weather_data_dir
        self.cities = [
            '杭州市', '宁波市', '温州市', '嘉兴市', '湖州市',
            '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市'
        ]
        self.aggregated_data = None

    def load_city_weather_data(self):
        """
        加载所有城市的天气数据

        Returns:
            dict: 城市名称为键，DataFrame为值的字典
        """
        city_data = {}

        for city in self.cities:
            file_pattern = os.path.join(self.weather_data_dir, f'{city}_*_weather_data.csv')
            files = glob.glob(file_pattern)

            if files:
                file_path = files[0]  # 取第一个匹配的文件
                try:
                    # 使用UTF-8编码读取CSV文件
                    df = pd.read_csv(file_path, encoding='utf-8')

                    # 数据清洗和标准化
                    df = self._clean_weather_data(df, city)
                    city_data[city] = df
                    logger.info(f"成功加载 {city} 天气数据，共 {len(df)} 条记录")

                except Exception as e:
                    logger.error(f"加载 {city} 天气数据失败: {str(e)}")
                    # 尝试其他编码
                    try:
                        df = pd.read_csv(file_path, encoding='gbk')
                        df = self._clean_weather_data(df, city)
                        city_data[city] = df
                        logger.info(f"使用GBK编码成功加载 {city} 天气数据，共 {len(df)} 条记录")
                    except Exception as e2:
                        logger.error(f"使用GBK编码加载 {city} 天气数据也失败: {str(e2)}")
            else:
                logger.warning(f"未找到 {city} 的天气数据文件")

        return city_data

    def _clean_weather_data(self, df, city_name):
        """
        清洗和标准化天气数据

        Args:
            df (DataFrame): 原始天气数据
            city_name (str): 城市名称

        Returns:
            DataFrame: 清洗后的数据
        """
        # 添加城市列
        df['城市'] = city_name

        # 标准化日期格式
        if '日期' in df.columns:
            df['日期'] = pd.to_datetime(df['日期'], errors='coerce')

        # 处理温度数据，提取数值
        if '高温' in df.columns:
            df['高温_数值'] = df['高温'].str.extract(r'(\d+)').astype(float)
        if '低温' in df.columns:
            df['低温_数值'] = df['低温'].str.extract(r'(\d+)').astype(float)

        # 处理AQI数据
        if 'AQI' in df.columns:
            df['AQI'] = pd.to_numeric(df['AQI'], errors='coerce')

        # 处理降水量数据
        if '降水量' in df.columns:
            df['降水量'] = pd.to_numeric(df['降水量'], errors='coerce')

        return df

    def aggregate_weather_data(self, method='mean'):
        """
        聚合各城市天气数据

        Args:
            method (str): 聚合方法 ('mean', 'median', 'weighted')

        Returns:
            DataFrame: 聚合后的浙江省天气数据
        """
        logger.info("开始聚合浙江省天气数据...")

        # 加载所有城市数据
        city_data = self.load_city_weather_data()

        if not city_data:
            logger.error("没有可用的城市天气数据")
            return None

        # 合并所有城市数据
        all_data = []
        for city, df in city_data.items():
            all_data.append(df)

        combined_df = pd.concat(all_data, ignore_index=True)

        # 按日期分组聚合
        if method == 'mean':
            aggregated = self._aggregate_by_mean(combined_df)
        elif method == 'median':
            aggregated = self._aggregate_by_median(combined_df)
        elif method == 'weighted':
            aggregated = self._aggregate_by_weighted_mean(combined_df)
        else:
            logger.error(f"不支持的聚合方法: {method}")
            return None

        self.aggregated_data = aggregated
        logger.info(f"聚合完成，共 {len(aggregated)} 条记录")

        return aggregated

    def _aggregate_by_mean(self, combined_df):
        """
        使用平均值聚合数据

        Args:
            combined_df (DataFrame): 合并后的所有城市数据

        Returns:
            DataFrame: 聚合后的数据
        """
        # 按日期分组，计算数值字段的平均值
        numeric_cols = ['高温_数值', '低温_数值', 'AQI', '降水量']

        # 过滤存在的列
        available_numeric_cols = [col for col in numeric_cols if col in combined_df.columns]

        if not available_numeric_cols:
            logger.warning("没有找到可聚合的数值列")
            return combined_df.drop_duplicates(subset=['日期']).reset_index(drop=True)

        # 按日期分组聚合
        aggregated = combined_df.groupby('日期').agg({
            **{col: 'mean' for col in available_numeric_cols},
            '天气状况': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0],
            '风向风力': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0],
            '高温': 'first',  # 保留原始格式
            '低温': 'first'   # 保留原始格式
        }).reset_index()

        # 重新计算平均温度的字符串格式
        if '高温_数值' in aggregated.columns:
            aggregated['高温'] = aggregated['高温_数值'].round().astype(int).astype(str) + '℃'
        if '低温_数值' in aggregated.columns:
            aggregated['低温'] = aggregated['低温_数值'].round().astype(int).astype(str) + '℃'

        return aggregated

    def _aggregate_by_median(self, combined_df):
        """
        使用中位数聚合数据

        Args:
            combined_df (DataFrame): 合并后的所有城市数据

        Returns:
            DataFrame: 聚合后的数据
        """
        # 按日期分组，计算数值字段的中位数
        numeric_cols = ['高温_数值', '低温_数值', 'AQI', '降水量']
        available_numeric_cols = [col for col in numeric_cols if col in combined_df.columns]

        if not available_numeric_cols:
            logger.warning("没有找到可聚合的数值列")
            return combined_df.drop_duplicates(subset=['日期']).reset_index(drop=True)

        # 按日期分组聚合
        aggregated = combined_df.groupby('日期').agg({
            **{col: 'median' for col in available_numeric_cols},
            '天气状况': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0],
            '风向风力': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0],
            '高温': 'first',
            '低温': 'first'
        }).reset_index()

        # 重新计算中位数温度的字符串格式
        if '高温_数值' in aggregated.columns:
            aggregated['高温'] = aggregated['高温_数值'].round().astype(int).astype(str) + '℃'
        if '低温_数值' in aggregated.columns:
            aggregated['低温'] = aggregated['低温_数值'].round().astype(int).astype(str) + '℃'

        return aggregated

    def _aggregate_by_weighted_mean(self, combined_df):
        """
        使用加权平均聚合数据（根据城市人口或重要性加权）

        Args:
            combined_df (DataFrame): 合并后的所有城市数据

        Returns:
            DataFrame: 聚合后的数据
        """
        # 定义城市权重（基于人口和经济重要性）
        city_weights = {
            '杭州市': 0.15,  # 省会，经济中心
            '宁波市': 0.12,  # 重要港口城市
            '温州市': 0.10,  # 经济发达
            '嘉兴市': 0.08,
            '湖州市': 0.08,
            '绍兴市': 0.09,
            '金华市': 0.09,
            '衢州市': 0.07,
            '舟山市': 0.06,
            '台州市': 0.09,
            '丽水市': 0.07
        }

        # 添加权重列
        combined_df['权重'] = combined_df['城市'].map(city_weights).fillna(0.05)

        # 按日期分组，计算加权平均
        numeric_cols = ['高温_数值', '低温_数值', 'AQI', '降水量']
        available_numeric_cols = [col for col in numeric_cols if col in combined_df.columns]

        def weighted_mean(group, col):
            """计算加权平均值"""
            weights = group['权重']
            values = group[col]
            # 过滤掉NaN值
            mask = ~values.isna()
            if mask.sum() == 0:
                return np.nan
            return (values[mask] * weights[mask]).sum() / weights[mask].sum()

        aggregated_data = []
        for date, group in combined_df.groupby('日期'):
            row = {'日期': date}

            # 计算加权平均值
            for col in available_numeric_cols:
                row[col] = weighted_mean(group, col)

            # 处理分类变量（取最常见的值）
            row['天气状况'] = group['天气状况'].mode().iloc[0] if len(group['天气状况'].mode()) > 0 else group['天气状况'].iloc[0]
            row['风向风力'] = group['风向风力'].mode().iloc[0] if len(group['风向风力'].mode()) > 0 else group['风向风力'].iloc[0]

            aggregated_data.append(row)

        aggregated = pd.DataFrame(aggregated_data)

        # 重新计算加权平均温度的字符串格式
        if '高温_数值' in aggregated.columns:
            aggregated['高温'] = aggregated['高温_数值'].round().astype(int).astype(str) + '℃'
        if '低温_数值' in aggregated.columns:
            aggregated['低温'] = aggregated['低温_数值'].round().astype(int).astype(str) + '℃'

        return aggregated

    def save_aggregated_data(self, output_path=None):
        """
        保存聚合后的天气数据

        Args:
            output_path (str): 输出文件路径，默认为weather_data/浙江省聚合天气数据.csv

        Returns:
            str: 保存的文件路径
        """
        if self.aggregated_data is None:
            logger.error("没有聚合数据可保存，请先运行aggregate_weather_data()")
            return None

        if output_path is None:
            output_path = os.path.join(self.weather_data_dir, '浙江省聚合天气数据.csv')

        try:
            # 使用UTF-8编码保存，添加BOM以确保中文正确显示
            self.aggregated_data.to_csv(output_path, index=False, encoding='utf-8-sig')
            logger.info(f"聚合天气数据已保存到: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"保存聚合数据失败: {str(e)}")
            return None


class WeatherExcelIntegrator:
    """天气数据与Excel文件整合器"""

    def __init__(self):
        """初始化整合器"""
        self.weather_data = None
        self.excel_data = None
        self.integrated_data = None

    def load_weather_data(self, weather_file_path):
        """
        加载天气数据

        Args:
            weather_file_path (str): 天气数据文件路径

        Returns:
            bool: 加载是否成功
        """
        try:
            # 尝试UTF-8编码
            self.weather_data = pd.read_csv(weather_file_path, encoding='utf-8')

            # 确保日期列为datetime格式
            if '日期' in self.weather_data.columns:
                self.weather_data['日期'] = pd.to_datetime(self.weather_data['日期'], errors='coerce')

            logger.info(f"成功加载天气数据: {weather_file_path}, 共 {len(self.weather_data)} 条记录")
            return True

        except Exception as e:
            logger.error(f"加载天气数据失败: {str(e)}")
            # 尝试其他编码
            try:
                self.weather_data = pd.read_csv(weather_file_path, encoding='gbk')
                if '日期' in self.weather_data.columns:
                    self.weather_data['日期'] = pd.to_datetime(self.weather_data['日期'], errors='coerce')
                logger.info(f"使用GBK编码成功加载天气数据: {weather_file_path}")
                return True
            except Exception as e2:
                logger.error(f"使用GBK编码加载天气数据也失败: {str(e2)}")
                return False

    def load_excel_data(self, excel_file_path):
        """
        加载Excel数据

        Args:
            excel_file_path (str): Excel文件路径

        Returns:
            bool: 加载是否成功
        """
        try:
            # 读取Excel文件
            self.excel_data = pd.read_excel(excel_file_path)

            # 尝试识别日期列
            date_columns = []
            for col in self.excel_data.columns:
                if any(keyword in str(col).lower() for keyword in ['日期', 'date', '时间', 'time']):
                    date_columns.append(col)

            if not date_columns:
                # 如果没有明确的日期列，尝试转换第一列
                first_col = self.excel_data.columns[0]
                try:
                    self.excel_data[first_col] = pd.to_datetime(self.excel_data[first_col], errors='coerce')
                    if not self.excel_data[first_col].isna().all():
                        date_columns.append(first_col)
                        logger.info(f"将第一列 '{first_col}' 识别为日期列")
                except:
                    pass

            # 标准化日期列
            for col in date_columns:
                try:
                    # 尝试多种日期格式解析
                    original_data = self.excel_data[col].copy()

                    # 首先尝试直接转换
                    self.excel_data[col] = pd.to_datetime(self.excel_data[col], errors='coerce')

                    # 如果转换后的日期都在1970年附近，说明可能是数字格式的日期
                    if self.excel_data[col].dt.year.min() < 1990:
                        logger.info(f"检测到列 '{col}' 可能是数字格式的日期，尝试特殊处理...")

                        # 尝试将数字转换为字符串再解析
                        try:
                            # 假设格式是YYYYMMDD
                            date_strings = original_data.astype(str).str.replace('.0', '')
                            self.excel_data[col] = pd.to_datetime(date_strings, format='%Y%m%d', errors='coerce')
                            logger.info(f"使用YYYYMMDD格式成功解析日期")
                        except:
                            try:
                                # 尝试其他可能的格式
                                date_strings = original_data.astype(str)
                                # 移除小数点
                                date_strings = date_strings.str.replace('.0', '')
                                # 尝试插入分隔符
                                if len(date_strings.iloc[0]) == 8:  # YYYYMMDD
                                    formatted_dates = date_strings.str[:4] + '-' + date_strings.str[4:6] + '-' + date_strings.str[6:8]
                                    self.excel_data[col] = pd.to_datetime(formatted_dates, errors='coerce')
                                    logger.info(f"使用YYYY-MM-DD格式成功解析日期")
                                else:
                                    logger.warning(f"无法识别列 '{col}' 的日期格式")
                                    continue
                            except:
                                logger.warning(f"列 '{col}' 的特殊日期格式处理失败")
                                continue

                    # 重命名为标准日期列名
                    if col != '日期':
                        self.excel_data['日期'] = self.excel_data[col]
                        logger.info(f"将列 '{col}' 重命名为 '日期'")
                    break
                except Exception as e:
                    logger.warning(f"处理列 '{col}' 时出错: {e}")
                    continue

            logger.info(f"成功加载Excel数据: {excel_file_path}, 共 {len(self.excel_data)} 条记录")
            logger.info(f"Excel数据列: {list(self.excel_data.columns)}")
            return True

        except Exception as e:
            logger.error(f"加载Excel数据失败: {str(e)}")
            return False

    def integrate_data(self, date_match_strategy='inner'):
        """
        整合天气数据和Excel数据

        Args:
            date_match_strategy (str): 日期匹配策略
                - 'inner': 只保留两个数据集都有的日期
                - 'left': 保留Excel数据的所有日期，天气数据不匹配的用NaN填充
                - 'weather_only': 只保留天气数据中存在的日期

        Returns:
            bool: 整合是否成功
        """
        if self.weather_data is None:
            logger.error("天气数据未加载")
            return False

        if self.excel_data is None:
            logger.error("Excel数据未加载")
            return False

        if '日期' not in self.weather_data.columns:
            logger.error("天气数据中没有找到日期列")
            return False

        if '日期' not in self.excel_data.columns:
            logger.error("Excel数据中没有找到日期列")
            return False

        try:
            # 确保两个数据集的日期列都是datetime格式
            self.weather_data['日期'] = pd.to_datetime(self.weather_data['日期'], errors='coerce')
            self.excel_data['日期'] = pd.to_datetime(self.excel_data['日期'], errors='coerce')

            # 移除日期为NaN的行
            weather_clean = self.weather_data.dropna(subset=['日期'])
            excel_clean = self.excel_data.dropna(subset=['日期'])

            logger.info(f"天气数据有效日期范围: {weather_clean['日期'].min()} 到 {weather_clean['日期'].max()}")
            logger.info(f"Excel数据有效日期范围: {excel_clean['日期'].min()} 到 {excel_clean['日期'].max()}")

            # 根据策略进行数据合并
            if date_match_strategy == 'inner':
                # 内连接：只保留两个数据集都有的日期
                self.integrated_data = pd.merge(excel_clean, weather_clean, on='日期', how='inner')
                logger.info("使用内连接策略，只保留两个数据集都有的日期")

            elif date_match_strategy == 'left':
                # 左连接：保留Excel数据的所有日期
                self.integrated_data = pd.merge(excel_clean, weather_clean, on='日期', how='left')
                logger.info("使用左连接策略，保留Excel数据的所有日期")

            elif date_match_strategy == 'weather_only':
                # 只保留天气数据中存在的日期，丢弃Excel中多余的日期
                common_dates = set(excel_clean['日期']).intersection(set(weather_clean['日期']))
                excel_filtered = excel_clean[excel_clean['日期'].isin(common_dates)]
                weather_filtered = weather_clean[weather_clean['日期'].isin(common_dates)]
                self.integrated_data = pd.merge(excel_filtered, weather_filtered, on='日期', how='inner')
                logger.info("只保留天气数据中存在的日期")

            else:
                logger.error(f"不支持的日期匹配策略: {date_match_strategy}")
                return False

            # 按日期排序
            self.integrated_data = self.integrated_data.sort_values('日期').reset_index(drop=True)

            logger.info(f"数据整合完成，共 {len(self.integrated_data)} 条记录")
            logger.info(f"整合后的列: {list(self.integrated_data.columns)}")

            return True

        except Exception as e:
            logger.error(f"数据整合失败: {str(e)}")
            return False

    def save_integrated_data(self, output_path, file_format='excel'):
        """
        保存整合后的数据

        Args:
            output_path (str): 输出文件路径
            file_format (str): 文件格式 ('excel' 或 'csv')

        Returns:
            bool: 保存是否成功
        """
        if self.integrated_data is None:
            logger.error("没有整合数据可保存，请先运行integrate_data()")
            return False

        try:
            if file_format.lower() == 'excel':
                # 保存为Excel文件
                self.integrated_data.to_excel(output_path, index=False)
                logger.info(f"整合数据已保存为Excel文件: {output_path}")

            elif file_format.lower() == 'csv':
                # 保存为CSV文件，使用UTF-8编码，添加BOM以确保中文正确显示
                self.integrated_data.to_csv(output_path, index=False, encoding='utf-8-sig')
                logger.info(f"整合数据已保存为CSV文件: {output_path}")

            else:
                logger.error(f"不支持的文件格式: {file_format}")
                return False

            return True

        except Exception as e:
            logger.error(f"保存整合数据失败: {str(e)}")
            return False


def main():
    """
    主函数：执行完整的数据处理流程
    """
    logger.info("开始执行浙江省天气数据处理任务...")

    # Task 1: 聚合浙江省天气数据
    logger.info("=" * 50)
    logger.info("任务1: 聚合浙江省天气数据")
    logger.info("=" * 50)

    aggregator = ZhejiangWeatherAggregator()

    # 使用加权平均方法聚合数据（考虑城市重要性）
    aggregated_data = aggregator.aggregate_weather_data(method='weighted')

    if aggregated_data is not None:
        # 保存聚合数据
        aggregated_file = aggregator.save_aggregated_data()
        if aggregated_file:
            logger.info(f"任务1完成：聚合数据已保存到 {aggregated_file}")
        else:
            logger.error("任务1失败：无法保存聚合数据")
            return False
    else:
        logger.error("任务1失败：无法聚合天气数据")
        return False

    # Task 2: 整合天气数据与Excel文件
    logger.info("=" * 50)
    logger.info("任务2: 整合天气数据与Excel文件")
    logger.info("=" * 50)

    integrator = WeatherExcelIntegrator()

    # 加载聚合后的天气数据
    if not integrator.load_weather_data(aggregated_file):
        logger.error("任务2失败：无法加载聚合天气数据")
        return False

    # 加载Excel文件
    excel_file = 'data/李娟娟代理购电.xlsx'
    if not integrator.load_excel_data(excel_file):
        logger.error("任务2失败：无法加载Excel文件")
        return False

    # 整合数据（使用内连接策略，只保留两个数据集都有的日期）
    if not integrator.integrate_data(date_match_strategy='inner'):
        logger.error("任务2失败：无法整合数据")
        return False

    # 保存整合后的数据
    output_excel = 'data/整合天气数据_代理购电.xlsx'
    output_csv = 'data/整合天气数据_代理购电.csv'

    success_excel = integrator.save_integrated_data(output_excel, 'excel')
    success_csv = integrator.save_integrated_data(output_csv, 'csv')

    if success_excel and success_csv:
        logger.info(f"任务2完成：整合数据已保存到 {output_excel} 和 {output_csv}")
        logger.info("所有任务完成！")
        return True
    else:
        logger.error("任务2失败：无法保存整合数据")
        return False


if __name__ == "__main__":
    main()