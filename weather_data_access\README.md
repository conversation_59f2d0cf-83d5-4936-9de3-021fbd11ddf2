# 代理购电数据与天气数据整合工具

## 功能概述

本工具用于整合李娟娟代理购电.xlsx文件和浙江省聚合天气数据.csv文件，生成包含电力数据和天气数据的综合数据集。

## 文件结构

```
weather_data_access/
├── integrate_power_weather.py    # 核心整合模块
├── run_integration.py           # 运行脚本
├── 1.py                        # 原有的天气数据聚合模块
├── run_weather_processing.py   # 原有的运行脚本
└── README.md                   # 本说明文档
```

## 主要功能

### 1. 数据加载
- **代理购电数据**: 自动识别并转换YYYYMMDD格式的日期
- **天气数据**: 支持UTF-8和GBK编码的CSV文件

### 2. 数据整合
- 按日期字段进行数据合并
- 支持多种合并策略：
  - `inner`: 只保留两个数据集都有的日期（默认）
  - `left`: 保留代理购电数据的所有日期
  - `outer`: 保留所有日期

### 3. 数据输出
- 同时生成Excel和CSV格式的整合文件
- 文件名包含时间戳，避免覆盖
- 自动保存到data目录

## 使用方法

### 快速开始

1. 确保数据文件存在：
   - `data/李娟娟代理购电.xlsx`
   - `weather_data/浙江省聚合天气数据.csv`

2. 运行整合脚本：
   ```bash
   cd weather_data_access
   python run_integration.py
   ```

3. 查看输出文件：
   - Excel格式: `data/整合天气数据_代理购电_YYYYMMDD_HHMMSS.xlsx`
   - CSV格式: `data/整合天气数据_代理购电_YYYYMMDD_HHMMSS.csv`

### 程序化使用

```python
from integrate_power_weather import PowerWeatherIntegrator

# 创建整合器实例
integrator = PowerWeatherIntegrator()

# 加载数据
integrator.load_power_data('../data/李娟娟代理购电.xlsx')
integrator.load_weather_data('../weather_data/浙江省聚合天气数据.csv')

# 整合数据
integrator.integrate_data(merge_strategy='inner')

# 保存结果
excel_path, csv_path = integrator.save_integrated_data()

# 获取数据摘要
summary = integrator.get_data_summary()
print(summary)
```

## 输出数据结构

整合后的数据包含以下字段：

### 电力数据字段
- `日期`: 日期（YYYY-MM-DD格式）
- `居民`: 居民用电量
- `农业`: 农业用电量
- `代理购电`: 代理购电量

### 天气数据字段
- `高温_数值`: 当日最高温度（数值）
- `低温_数值`: 当日最低温度（数值）
- `AQI`: 空气质量指数
- `降水量`: 降水量（毫米）
- `天气状况`: 天气状况描述
- `风向风力`: 风向和风力描述
- `高温`: 最高温度（带单位）
- `低温`: 最低温度（带单位）

## 日志文件

程序运行时会生成详细的日志文件：
- `power_weather_integration.log`: 整合过程的详细日志

## 注意事项

1. **日期格式**: 代理购电数据中的日期格式为YYYYMMDD（如20220501），程序会自动转换为标准日期格式

2. **编码支持**: 天气数据文件支持UTF-8和GBK编码，程序会自动检测并使用正确的编码

3. **数据匹配**: 使用内连接策略时，只保留两个数据集都存在的日期，确保数据完整性

4. **文件路径**: 确保数据文件路径正确，程序会从weather_data_access目录访问上级目录的文件

## 错误处理

程序包含完善的错误处理机制：
- 文件不存在时会给出明确提示
- 日期转换失败时会记录警告信息
- 数据整合失败时会输出详细错误信息

## 日数据转月度数据功能

### 新增功能模块

#### 1. 日数据转月度数据转换器 (`daily_to_monthly_converter.py`)

**主要功能：**
- 将日天气数据聚合为月度统计数据
- 对非数值数据进行量化处理
- 计算多种统计指标（平均值、最大值、最小值、标准差等）

**数据量化方法：**

1. **天气状况量化**：
   - 晴: 1.0 (最好天气)
   - 多云: 0.7
   - 阴: 0.4
   - 小雨: 0.2
   - 中雨: 0.1
   - 大雨: 0.05
   - 雪: 0.1
   - 未知: 0.5 (中性值)

2. **风力等级量化**：
   - 直接提取数字等级 (1级=1, 2级=2, ..., 12级=12)

3. **风向量化**：
   - 基于方位角度 (北风=0°, 东北风=45°, 东风=90°, 等)

**月度统计指标：**
- **温度**: 平均值、最大值、最小值、标准差
- **空气质量**: 平均AQI、最高AQI、最低AQI、AQI标准差
- **降水**: 总降水量、平均降水量、最大日降水量、降水天数、降水频率
- **天气**: 平均天气指数、最差天气指数
- **风力风向**: 平均风力等级、最大风力等级、平均风向角度
- **衍生指标**: 温差、降水频率

#### 2. 使用方法

**快速转换：**
```bash
cd weather_data_access
python run_monthly_conversion.py
```

**程序化使用：**
```python
from daily_to_monthly_converter import DailyToMonthlyConverter

converter = DailyToMonthlyConverter()
converter.load_daily_data('../weather_data/浙江省聚合天气数据.csv')
converter.convert_to_monthly()
excel_path, csv_path = converter.save_monthly_data()
```

**输出文件：**
- Excel格式: `浙江省月度天气数据_YYYYMMDD_HHMMSS.xlsx`
- CSV格式: `浙江省月度天气数据_YYYYMMDD_HHMMSS.csv`

#### 3. 月度数据字段说明

| 字段名 | 说明 | 单位 |
|--------|------|------|
| 年月 | 年月标识 | YYYY-MM |
| 平均高温 | 月平均最高温度 | ℃ |
| 最高温度 | 月内最高温度 | ℃ |
| 最低高温 | 月内最低的日最高温度 | ℃ |
| 高温标准差 | 日最高温度的标准差 | ℃ |
| 平均低温 | 月平均最低温度 | ℃ |
| 最高低温 | 月内最高的日最低温度 | ℃ |
| 最低低温 | 月内最低温度 | ℃ |
| 低温标准差 | 日最低温度的标准差 | ℃ |
| 平均AQI | 月平均空气质量指数 | - |
| 最高AQI | 月内最高AQI | - |
| 最低AQI | 月内最低AQI | - |
| AQI标准差 | AQI的标准差 | - |
| 总降水量 | 月总降水量 | mm |
| 平均降水量 | 月平均日降水量 | mm |
| 最大日降水量 | 月内最大日降水量 | mm |
| 降水天数 | 有降水的天数 | 天 |
| 平均天气指数 | 量化后的平均天气状况 | 0-1 |
| 最差天气指数 | 量化后的最差天气状况 | 0-1 |
| 平均风力等级 | 月平均风力等级 | 级 |
| 最大风力等级 | 月内最大风力等级 | 级 |
| 平均风向角度 | 月平均风向角度 | 度 |
| 月天数 | 该月的天数 | 天 |
| 年份 | 年份 | - |
| 月份 | 月份 | - |
| 温差 | 平均高温与平均低温的差值 | ℃ |
| 降水频率 | 降水天数/月天数 | 0-1 |

## 依赖库

- pandas: 数据处理
- openpyxl: Excel文件读写
- numpy: 数值计算

确保安装了所需的依赖库：
```bash
pip install pandas openpyxl numpy
```

## 一键运行所有任务

### 使用主脚本 (`run_all_tasks.py`)

```bash
cd weather_data_access
python run_all_tasks.py
```

该脚本提供交互式菜单，可以选择执行：
1. 全部任务（数据整合 + 日转月度 + 可视化）
2. 仅数据整合
3. 仅日转月度转换
4. 仅数据可视化
5. 数据整合 + 日转月度转换

### 可视化功能（可选）

如需生成数据可视化图表，请安装额外依赖：
```bash
pip install matplotlib seaborn
```

可视化功能将生成以下图表：
- 月度温度趋势图
- 月度AQI和降水趋势图
- 月度天气指数和风力趋势图
- 季节性分析图

## 文件输出说明

### 数据整合输出
- `整合天气数据_代理购电_YYYYMMDD_HHMMSS.xlsx`
- `整合天气数据_代理购电_YYYYMMDD_HHMMSS.csv`

### 月度数据输出
- `浙江省月度天气数据_YYYYMMDD_HHMMSS.xlsx`
- `浙江省月度天气数据_YYYYMMDD_HHMMSS.csv`

### 可视化输出（如果启用）
- `月度温度趋势.png`
- `月度AQI和降水趋势.png`
- `月度天气指数和风力趋势.png`
- `季节性分析.png`

## 日志文件

- `power_weather_integration.log`: 数据整合日志
- `daily_to_monthly.log`: 日转月度转换日志

## 数据量化说明

### 天气状况量化规则
天气状况被量化为0-1的数值，1表示最好的天气：
- 晴天: 1.0
- 多云: 0.7
- 阴天: 0.4
- 小雨: 0.2
- 中雨: 0.1
- 大雨: 0.05
- 雪: 0.1
- 未知: 0.5

### 风向风力量化规则
- **风力等级**: 直接提取数字（1级=1, 2级=2, ...）
- **风向角度**: 基于方位角（北风=0°, 东北风=45°, 东风=90°, ...）

### 月度统计方法
- **温度**: 计算平均值、最大值、最小值、标准差
- **AQI**: 计算平均值、最大值、最小值、标准差
- **降水**: 计算总量、平均值、最大值、降水天数、降水频率
- **天气指数**: 计算平均值和最差值
- **风力风向**: 计算平均风力、最大风力、平均风向角度
