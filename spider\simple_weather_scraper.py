#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版中国天气网爬虫 - 丽水市未来40天天气数据采集
目标网站: https://www.weather.com.cn/weather40d/101210801.shtml
作者: AI Assistant
创建时间: 2025-01-17
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import os
import time
import logging
import json
import re
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_weather_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleWeatherScraper:
    """简化版中国天气网爬虫类"""
    
    def __init__(self):
        self.base_url = "https://www.weather.com.cn"
        self.target_url = "https://www.weather.com.cn/weather40d/101210801.shtml"  # 丽水市40天天气
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
        
        # 数据保存目录
        self.data_dir = './weather_data'
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            
        # 数据列名
        self.columns = ['日期', '星期', '天气状况', '高温', '低温', '风向风力', '降水概率']
    
    def get_weather_data(self):
        """获取天气数据"""
        try:
            logger.info("开始获取丽水市40天天气数据...")
            
            session = requests.Session()
            session.headers.update(self.headers)
            
            # 首先尝试获取主页面
            response = session.get(self.target_url, timeout=15)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            logger.info(f"页面获取成功，状态码: {response.status_code}")
            
            # 保存HTML用于调试
            debug_file = os.path.join(self.data_dir, 'debug_page.html')
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            logger.info(f"页面内容已保存到: {debug_file}")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尝试多种方法解析天气数据
            weather_data = []
            
            # 方法1: 查找包含天气数据的表格
            weather_data = self.parse_method_1(soup)
            if weather_data:
                logger.info(f"方法1成功获取 {len(weather_data)} 条数据")
                return weather_data
            
            # 方法2: 查找特定的div容器
            weather_data = self.parse_method_2(soup)
            if weather_data:
                logger.info(f"方法2成功获取 {len(weather_data)} 条数据")
                return weather_data
            
            # 方法3: 从页面文本中提取
            weather_data = self.parse_method_3(soup)
            if weather_data:
                logger.info(f"方法3成功获取 {len(weather_data)} 条数据")
                return weather_data
            
            # 如果都失败了，尝试API接口
            weather_data = self.try_api_methods(session)
            if weather_data:
                logger.info(f"API方法成功获取 {len(weather_data)} 条数据")
                return weather_data
            
            logger.warning("所有方法都未能获取到天气数据")
            return []
            
        except Exception as e:
            logger.error(f"获取天气数据失败: {e}")
            return []
    
    def parse_method_1(self, soup):
        """方法1: 查找表格数据"""
        try:
            weather_data = []
            
            # 查找可能的表格
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 4:  # 至少有4列数据
                        row_text = ' '.join([cell.get_text(strip=True) for cell in cells])
                        if self.contains_weather_info(row_text):
                            parsed_data = self.parse_weather_text(row_text)
                            if parsed_data:
                                weather_data.append(parsed_data)
            
            return weather_data
            
        except Exception as e:
            logger.debug(f"方法1解析失败: {e}")
            return []
    
    def parse_method_2(self, soup):
        """方法2: 查找div容器"""
        try:
            weather_data = []
            
            # 查找可能包含天气数据的div
            divs = soup.find_all('div')
            for div in divs:
                div_text = div.get_text(strip=True)
                if self.contains_weather_info(div_text) and len(div_text) > 20:
                    parsed_data = self.parse_weather_text(div_text)
                    if parsed_data:
                        weather_data.append(parsed_data)
            
            # 去重
            weather_data = self.remove_duplicates(weather_data)
            return weather_data
            
        except Exception as e:
            logger.debug(f"方法2解析失败: {e}")
            return []
    
    def parse_method_3(self, soup):
        """方法3: 从页面文本中提取"""
        try:
            weather_data = []
            
            # 获取页面所有文本
            page_text = soup.get_text()
            
            # 按行分割
            lines = page_text.split('\n')
            
            for line in lines:
                line = line.strip()
                if self.contains_weather_info(line) and len(line) > 10:
                    parsed_data = self.parse_weather_text(line)
                    if parsed_data:
                        weather_data.append(parsed_data)
            
            # 去重
            weather_data = self.remove_duplicates(weather_data)
            return weather_data
            
        except Exception as e:
            logger.debug(f"方法3解析失败: {e}")
            return []
    
    def try_api_methods(self, session):
        """尝试API接口"""
        try:
            # 尝试不同的API端点和数据源
            api_urls = [
                # 尝试获取40天预报的API
                "https://d1.weather.com.cn/calendar_new/101210801.html",
                "https://d1.weather.com.cn/weather_index/101210801.html",
                "https://d1.weather.com.cn/sk_2d/101210801.html",
                "https://forecast.weather.com.cn/town/weathern/101210801.shtml",
                # 尝试移动端API
                "https://m.weather.com.cn/mweather40d/101210801.shtml",
                # 尝试其他可能的数据接口
                "https://www.weather.com.cn/data/sk/101210801.html",
                "https://www.weather.com.cn/data/cityinfo/101210801.html"
            ]

            for api_url in api_urls:
                try:
                    logger.info(f"尝试API: {api_url}")
                    response = session.get(api_url, timeout=10)
                    if response.status_code == 200:
                        logger.info(f"API响应成功: {len(response.text)} 字符")

                        # 保存API响应用于调试
                        api_filename = f"api_response_{api_url.split('/')[-1]}.html"
                        api_filepath = os.path.join(self.data_dir, api_filename)
                        with open(api_filepath, 'w', encoding='utf-8') as f:
                            f.write(response.text)

                        # 尝试解析为JSON
                        try:
                            data = response.json()
                            weather_data = self.parse_json_data(data)
                            if weather_data:
                                logger.info(f"JSON解析成功: {len(weather_data)} 条数据")
                                return weather_data
                        except:
                            # 如果不是JSON，当作HTML解析
                            soup = BeautifulSoup(response.text, 'html.parser')
                            weather_data = self.parse_method_1(soup)
                            if not weather_data:
                                weather_data = self.parse_method_2(soup)
                            if weather_data:
                                logger.info(f"HTML解析成功: {len(weather_data)} 条数据")
                                return weather_data
                except Exception as e:
                    logger.debug(f"API {api_url} 失败: {e}")
                    continue

            # 尝试构造40天天气的直接数据请求
            weather_data = self.try_direct_weather_api(session)
            if weather_data:
                return weather_data

            return []

        except Exception as e:
            logger.debug(f"API方法失败: {e}")
            return []

    def try_direct_weather_api(self, session):
        """尝试直接的天气数据API"""
        try:
            # 构造可能的天气数据API URL
            city_code = "101210801"  # 丽水市代码

            # 尝试不同的数据格式
            direct_apis = [
                f"https://www.weather.com.cn/data/sk/{city_code}.html",
                f"https://www.weather.com.cn/data/cityinfo/{city_code}.html",
                f"https://d1.weather.com.cn/dingzhi/{city_code}.html",
                f"https://d1.weather.com.cn/calendar/{city_code}.html"
            ]

            for api_url in direct_apis:
                try:
                    logger.info(f"尝试直接API: {api_url}")
                    response = session.get(api_url, timeout=10)
                    if response.status_code == 200:
                        try:
                            # 尝试解析JSON
                            data = response.json()
                            weather_data = self.parse_json_data(data)
                            if weather_data:
                                logger.info(f"直接API成功: {len(weather_data)} 条数据")
                                return weather_data
                        except:
                            pass
                except Exception as e:
                    logger.debug(f"直接API {api_url} 失败: {e}")
                    continue

            return []

        except Exception as e:
            logger.debug(f"直接API方法失败: {e}")
            return []
    
    def parse_json_data(self, data):
        """解析JSON数据"""
        try:
            weather_data = []
            
            # 递归查找天气数据
            def find_weather_data(obj):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if key in ['weather', 'forecast', 'data', 'weatherinfo']:
                            if isinstance(value, list):
                                for item in value:
                                    parsed = self.parse_json_weather_item(item)
                                    if parsed:
                                        weather_data.append(parsed)
                            elif isinstance(value, dict):
                                parsed = self.parse_json_weather_item(value)
                                if parsed:
                                    weather_data.append(parsed)
                        else:
                            find_weather_data(value)
                elif isinstance(obj, list):
                    for item in obj:
                        find_weather_data(item)
            
            find_weather_data(data)
            return weather_data
            
        except Exception as e:
            logger.debug(f"解析JSON数据失败: {e}")
            return []
    
    def parse_json_weather_item(self, item):
        """解析JSON中的天气项目"""
        try:
            if not isinstance(item, dict):
                return None
            
            date = item.get('date', item.get('time', '未知'))
            weekday = item.get('week', item.get('weekday', '未知'))
            weather = item.get('weather', item.get('condition', '未知'))
            high_temp = item.get('high', item.get('max_temp', '未知'))
            low_temp = item.get('low', item.get('min_temp', '未知'))
            wind = item.get('wind', item.get('wind_direction', '未知'))
            precipitation = item.get('pop', item.get('precipitation', '未知'))
            
            return [date, weekday, weather, high_temp, low_temp, wind, precipitation]
            
        except Exception as e:
            logger.debug(f"解析JSON天气项目失败: {e}")
            return None
    
    def contains_weather_info(self, text):
        """检查文本是否包含天气信息"""
        weather_keywords = ['晴', '多云', '阴', '雨', '雪', '雾', '霾', '℃', '°C', '风', '级']
        date_keywords = ['月', '日', '今天', '明天', '后天', '周', '星期']
        
        has_weather = any(keyword in text for keyword in weather_keywords)
        has_date = any(keyword in text for keyword in date_keywords)
        
        return has_weather and has_date
    
    def parse_weather_text(self, text):
        """从文本中解析天气信息"""
        try:
            # 提取日期
            date = self.extract_date(text)
            if date == "未知":
                return None
            
            # 提取其他信息
            weekday = self.extract_weekday(text)
            weather = self.extract_weather_condition(text)
            high_temp, low_temp = self.extract_temperature(text)
            wind = self.extract_wind(text)
            precipitation = self.extract_precipitation(text)
            
            return [date, weekday, weather, high_temp, low_temp, wind, precipitation]
            
        except Exception as e:
            logger.debug(f"解析天气文本失败: {e}")
            return None
    
    def extract_date(self, text):
        """提取日期"""
        patterns = [
            r'(\d{1,2}月\d{1,2}日)',
            r'(\d{1,2}-\d{1,2})',
            r'(今天|明天|后天)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)
        return "未知"
    
    def extract_weekday(self, text):
        """提取星期"""
        patterns = [
            r'(周[一二三四五六日])',
            r'(星期[一二三四五六日])'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)
        return "未知"
    
    def extract_weather_condition(self, text):
        """提取天气状况"""
        weather_keywords = ['晴', '多云', '阴', '雨', '雪', '雾', '霾', '雷阵雨', '小雨', '中雨', '大雨', '暴雨']
        
        found_weather = []
        for keyword in weather_keywords:
            if keyword in text:
                found_weather.append(keyword)
        
        if found_weather:
            return '/'.join(found_weather[:2])  # 最多取两个
        return "未知"
    
    def extract_temperature(self, text):
        """提取温度"""
        temp_patterns = [
            r'(\d+)℃.*?(\d+)℃',
            r'(\d+)°.*?(\d+)°',
            r'高温[：:]?(\d+).*?低温[：:]?(\d+)',
            r'(\d+)[-~](\d+)℃'
        ]
        
        for pattern in temp_patterns:
            match = re.search(pattern, text)
            if match:
                high_temp = f"{match.group(1)}℃"
                low_temp = f"{match.group(2)}℃"
                return high_temp, low_temp
        
        # 单个温度
        single_temp = re.search(r'(\d+)℃', text)
        if single_temp:
            temp = f"{single_temp.group(1)}℃"
            return temp, "未知"
        
        return "未知", "未知"
    
    def extract_wind(self, text):
        """提取风向风力"""
        wind_patterns = [
            r'([东西南北]+风\d+级)',
            r'([东西南北]+风\d+-\d+级)',
            r'(无持续风向.*?级)'
        ]
        
        for pattern in wind_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)
        return "未知"
    
    def extract_precipitation(self, text):
        """提取降水概率"""
        precip_patterns = [
            r'降水概率[：:]?(\d+)%',
            r'(\d+)%.*?降水',
            r'降雨概率[：:]?(\d+)%'
        ]
        
        for pattern in precip_patterns:
            match = re.search(pattern, text)
            if match:
                return f"{match.group(1)}%"
        return "未知"
    
    def remove_duplicates(self, weather_data):
        """去除重复数据"""
        seen = set()
        unique_data = []
        
        for item in weather_data:
            if item and len(item) >= 3:
                key = (item[0], item[2])  # 使用日期和天气状况作为唯一键
                if key not in seen:
                    seen.add(key)
                    unique_data.append(item)
        
        return unique_data
    
    def save_data(self, weather_data):
        """保存数据到CSV文件"""
        if not weather_data:
            logger.warning("没有数据需要保存")
            return False
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(weather_data, columns=self.columns)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"丽水市_40天天气预报_{timestamp}.csv"
            filepath = os.path.join(self.data_dir, filename)
            
            # 保存数据
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"✓ 数据已保存到 {filepath} ({len(df)} 条记录)")
            
            # 显示前几条数据
            logger.info("前5条数据预览:")
            print(df.head().to_string(index=False))
            
            return True
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return False
    
    def run_scraper(self):
        """运行爬虫主程序"""
        logger.info("=" * 60)
        logger.info("简化版中国天气网爬虫启动 - 丽水市40天天气预报")
        logger.info(f"目标URL: {self.target_url}")
        logger.info("=" * 60)
        
        # 获取天气数据
        weather_data = self.get_weather_data()
        
        # 保存数据
        if weather_data:
            success = self.save_data(weather_data)
            if success:
                logger.info("爬取任务完成！")
            else:
                logger.error("数据保存失败")
        else:
            logger.error("未能获取到天气数据")
            logger.info("请检查网络连接或网站结构是否发生变化")
        
        logger.info("=" * 60)

def main():
    """主函数"""
    scraper = SimpleWeatherScraper()
    
    try:
        scraper.run_scraper()
    except KeyboardInterrupt:
        logger.info("\n用户中断爬取任务")
    except Exception as e:
        logger.error(f"爬取任务异常: {e}")

if __name__ == "__main__":
    main()
