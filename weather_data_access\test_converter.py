#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修改后的日天气数据转月度数据转换器
针对丽水市天气数据进行测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from daily_to_monthly_converter import DailyToMonthlyConverter
import pandas as pd
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_converter():
    """测试转换器功能"""
    logger.info("=" * 60)
    logger.info("测试丽水市天气数据转换器")
    logger.info("=" * 60)
    
    # 初始化转换器
    converter = DailyToMonthlyConverter()
    
    # 文件路径
    input_file = '../weather_data/丽水市_2022-2025_weather_data.csv'
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        logger.error(f"输入文件不存在: {input_file}")
        return False
    
    try:
        # 步骤1: 加载数据
        logger.info("步骤1: 加载丽水市日天气数据")
        if not converter.load_daily_data(input_file):
            logger.error("加载数据失败")
            return False
        
        # 显示原始数据信息
        logger.info("原始数据信息:")
        logger.info(f"  数据形状: {converter.daily_data.shape}")
        logger.info(f"  列名: {list(converter.daily_data.columns)}")
        logger.info("  前5行数据:")
        print(converter.daily_data.head().to_string(index=False))
        
        # 步骤2: 转换为月度数据
        logger.info("\n步骤2: 转换为月度数据")
        if not converter.convert_to_monthly():
            logger.error("转换失败")
            return False
        
        # 显示月度数据信息
        logger.info("月度数据信息:")
        logger.info(f"  数据形状: {converter.monthly_data.shape}")
        logger.info(f"  列名: {list(converter.monthly_data.columns)}")
        logger.info("  前5行数据:")
        print(converter.monthly_data.head().to_string(index=False))
        
        # 步骤3: 保存数据
        logger.info("\n步骤3: 保存月度数据")
        excel_path, csv_path = converter.save_monthly_data(
            output_dir='../data',
            city_name='丽水市'
        )
        
        if csv_path:
            logger.info("✓ 转换测试成功完成！")
            
            # 显示数据摘要
            summary = converter.get_monthly_summary()
            if summary:
                logger.info("\n数据摘要:")
                for key, value in summary.items():
                    if key != '数值统计':
                        logger.info(f"  {key}: {value}")
            
            return True
        else:
            logger.error("✗ 保存数据失败")
            return False
            
    except Exception as e:
        logger.error(f"测试过程出错: {e}")
        return False

def validate_output():
    """验证输出文件"""
    logger.info("\n验证输出文件...")
    
    data_dir = '../data'
    if not os.path.exists(data_dir):
        logger.warning("输出目录不存在")
        return
    
    # 查找最新的丽水市月度数据文件
    csv_files = [f for f in os.listdir(data_dir) 
                 if f.startswith('丽水市_月度天气数据_') and f.endswith('.csv')]
    
    if not csv_files:
        logger.warning("未找到输出的CSV文件")
        return
    
    # 选择最新的文件
    latest_file = max(csv_files, key=lambda x: os.path.getctime(os.path.join(data_dir, x)))
    file_path = os.path.join(data_dir, latest_file)
    
    try:
        # 读取并验证文件
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        logger.info(f"验证文件: {latest_file}")
        logger.info(f"  文件大小: {os.path.getsize(file_path)} 字节")
        logger.info(f"  数据行数: {len(df)}")
        logger.info(f"  数据列数: {len(df.columns)}")
        logger.info(f"  列名: {list(df.columns)}")
        
        # 检查关键列是否存在
        required_columns = ['年月', '年份', '月份', '平均高温', '平均低温', '温差']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.warning(f"缺少关键列: {missing_columns}")
        else:
            logger.info("✓ 所有关键列都存在")
        
        # 显示数据范围
        if '年月' in df.columns:
            logger.info(f"  时间范围: {df['年月'].min()} 到 {df['年月'].max()}")
        
        if '平均高温' in df.columns and '平均低温' in df.columns:
            logger.info(f"  温度范围: 高温 {df['平均高温'].min():.1f}-{df['平均高温'].max():.1f}℃")
            logger.info(f"            低温 {df['平均低温'].min():.1f}-{df['平均低温'].max():.1f}℃")
        
        logger.info("✓ 输出文件验证通过")
        
    except Exception as e:
        logger.error(f"验证输出文件失败: {e}")

def main():
    """主函数"""
    try:
        # 运行转换测试
        success = test_converter()
        
        if success:
            # 验证输出文件
            validate_output()
            
            logger.info("\n" + "=" * 60)
            logger.info("✓ 丽水市天气数据转换器测试完成！")
            logger.info("转换器已成功适配丽水市数据格式")
            logger.info("=" * 60)
        else:
            logger.error("\n✗ 转换器测试失败")
            
    except Exception as e:
        logger.error(f"测试异常: {e}")

if __name__ == "__main__":
    main()
